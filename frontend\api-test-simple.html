<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .auth-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简单API测试</h1>
        
        <div class="auth-info">
            <h3>认证状态</h3>
            <div id="auth-status">检查中...</div>
            <button onclick="simulateLogin()">模拟登录</button>
            <button onclick="clearAuth()">清除认证</button>
        </div>

        <h3>API测试</h3>
        <button onclick="testHealth()">健康检查</button>
        <button onclick="testFileList()">文件列表</button>
        <button onclick="testFolders()">文件夹列表</button>
        <button onclick="testDirectCall()">直接调用</button>
        
        <div id="result" class="result">等待测试...</div>
    </div>

    <script>
        // 简化的配置
        const API_BASE = 'http://localhost:8086/api';
        
        // 检查认证状态
        function checkAuth() {
            const authData = localStorage.getItem('fileShareAuth');
            const statusDiv = document.getElementById('auth-status');
            
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    statusDiv.innerHTML = `<span class="status-ok">✅ 已登录</span><br>用户: ${auth.user?.username || '未知'}<br>Token: ${auth.token ? auth.token.substring(0, 20) + '...' : '无'}`;
                } catch (e) {
                    statusDiv.innerHTML = '<span class="status-error">❌ 认证数据损坏</span>';
                }
            } else {
                statusDiv.innerHTML = '<span class="status-error">❌ 未登录</span>';
            }
        }

        function simulateLogin() {
            const mockAuth = {
                token: 'mock-token-' + Date.now(),
                user: {
                    id: 1,
                    username: 'test',
                    permissions: ['read', 'download']
                },
                loginTime: Date.now()
            };
            
            localStorage.setItem('fileShareAuth', JSON.stringify(mockAuth));
            checkAuth();
            log('已设置模拟登录信息');
        }

        function clearAuth() {
            localStorage.removeItem('fileShareAuth');
            checkAuth();
            log('已清除认证信息');
        }

        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.textContent += `[${timestamp}] ${message}\n`;
            result.scrollTop = result.scrollHeight;
        }

        async function testHealth() {
            log('测试健康检查...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                log(`✅ 健康检查成功: ${JSON.stringify(data)}`);
            } catch (error) {
                log(`❌ 健康检查失败: ${error.message}`);
            }
        }

        async function testFileList() {
            log('测试文件列表API...');
            try {
                const authData = localStorage.getItem('fileShareAuth');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (authData) {
                    const auth = JSON.parse(authData);
                    if (auth.token) {
                        headers['Authorization'] = `Bearer ${auth.token}`;
                    }
                }
                
                const response = await fetch(`${API_BASE}/files`, {
                    method: 'GET',
                    headers: headers
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ 文件列表API成功: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`❌ 文件列表API失败: ${error.message}`);
            }
        }

        async function testFolders() {
            log('测试文件夹列表API...');
            try {
                const authData = localStorage.getItem('fileShareAuth');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (authData) {
                    const auth = JSON.parse(authData);
                    if (auth.token) {
                        headers['Authorization'] = `Bearer ${auth.token}`;
                    }
                }
                
                const response = await fetch(`${API_BASE}/files/folders`, {
                    method: 'GET',
                    headers: headers
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ 文件夹列表API成功: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`❌ 文件夹列表API失败: ${error.message}`);
            }
        }

        async function testDirectCall() {
            log('测试直接API调用...');
            try {
                // 不带认证的调用
                const response1 = await fetch(`${API_BASE}/files`);
                log(`无认证调用状态: ${response1.status}`);
                
                if (response1.status === 401) {
                    log('✅ 正确返回401未授权状态');
                } else {
                    const data1 = await response1.json();
                    log(`无认证调用结果: ${JSON.stringify(data1)}`);
                }
                
                // 带认证的调用
                const authData = localStorage.getItem('fileShareAuth');
                if (authData) {
                    const auth = JSON.parse(authData);
                    const response2 = await fetch(`${API_BASE}/files`, {
                        headers: {
                            'Authorization': `Bearer ${auth.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    log(`带认证调用状态: ${response2.status}`);
                    
                    if (response2.ok) {
                        const data2 = await response2.json();
                        log(`带认证调用结果: ${JSON.stringify(data2, null, 2)}`);
                    } else {
                        const errorText = await response2.text();
                        log(`带认证调用错误: ${errorText}`);
                    }
                }
                
            } catch (error) {
                log(`❌ 直接调用失败: ${error.message}`);
            }
        }

        // 页面加载时检查认证状态
        window.addEventListener('load', () => {
            checkAuth();
            log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>
