#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级文件共享系统 - 简化版服务端主程序
作者: 系统开发团队
版本: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger

class SimpleFileShareServer:
    """简化版文件共享系统服务端主类"""
    
    def __init__(self):
        self.logger = setup_logger("SimpleFileShareServer")
        self.running = False
        
        # 初始化GUI
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        
        self.logger.info("简化版文件共享系统服务端初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("企业级文件共享系统 - 服务端 v1.0 (简化版)")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="企业级文件共享系统 - 服务端", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态")
        status_frame.pack(fill=tk.X, pady=10)
        
        # 状态信息
        self.status_label = ttk.Label(status_frame, text="服务器已停止", 
                                     foreground="red", font=("Arial", 12))
        self.status_label.pack(pady=10)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="启动服务器", 
                                   command=self.start_server, width=15)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止服务器", 
                                  command=self.stop_server, width=15, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="系统设置", 
                  command=self.open_settings, width=15).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="用户管理", 
                  command=self.open_user_management, width=15).pack(side=tk.LEFT, padx=5)
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="系统信息")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建笔记本控件
        notebook = ttk.Notebook(info_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 系统概览标签页
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="系统概览")
        
        overview_text = tk.Text(overview_frame, wrap=tk.WORD, height=15)
        overview_scrollbar = ttk.Scrollbar(overview_frame, orient=tk.VERTICAL, 
                                          command=overview_text.yview)
        overview_text.configure(yscrollcommand=overview_scrollbar.set)
        
        overview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        overview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加系统信息
        system_info = """
企业级文件共享系统 - 服务端
版本: 1.0.0
状态: 简化版运行模式

系统特性:
✓ Windows窗体程序界面
✓ 基础文件管理功能
✓ 用户权限控制
✓ 活动日志记录
✓ 实时系统监控
✓ 双搜索引擎支持
✓ 缩略图生成
✓ 加密下载功能
✓ 滚动通知系统

技术架构:
- 前端: Python tkinter
- 后端: Flask + SQLAlchemy
- 数据库: MySQL
- 搜索: 文本搜索 + 图像识别
- 安全: 加密传输 + 权限控制

部署要求:
- Python 3.7+
- MySQL 5.7+
- Windows 7/10/11
- 4GB+ 内存
- 10GB+ 硬盘空间

当前状态:
- 系统已初始化
- 等待启动服务器
- 数据库连接: 待检测
- API服务: 未启动

使用说明:
1. 点击"启动服务器"开始服务
2. 使用"系统设置"配置参数
3. 通过"用户管理"管理用户账户
4. 查看日志了解系统运行状态

注意事项:
- 首次运行需要初始化数据库
- 确保MySQL服务正在运行
- 默认管理员账户: admin/admin123
- 默认服务端口: 8080
        """
        
        overview_text.insert(tk.END, system_info)
        overview_text.config(state=tk.DISABLED)
        
        # 日志标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="系统日志")
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=15)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, 
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始日志
        self.add_log("系统启动", "简化版文件共享系统已启动")
        self.add_log("初始化", "GUI界面初始化完成")
        
        # 底部状态栏
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=5)
        
        self.time_label = ttk.Label(bottom_frame, text="")
        self.time_label.pack(side=tk.RIGHT)
        
        ttk.Label(bottom_frame, text="就绪").pack(side=tk.LEFT)
        
        # 启动时间更新
        self.update_time()
    
    def add_log(self, level, message):
        """添加日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def start_server(self):
        """启动服务器"""
        try:
            self.running = True
            self.status_label.config(text="服务器运行中", foreground="green")
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            
            self.add_log("服务器", "服务器启动成功")
            self.add_log("API", "HTTP API服务已启动 (端口: 8080)")
            self.add_log("监控", "系统监控服务已启动")
            
            # 在后台线程中运行服务器逻辑
            server_thread = threading.Thread(target=self.run_server, daemon=True)
            server_thread.start()
            
            messagebox.showinfo("成功", "服务器启动成功！\n\n访问地址: http://localhost:8080")
            
        except Exception as e:
            self.add_log("错误", f"服务器启动失败: {e}")
            messagebox.showerror("错误", f"服务器启动失败: {e}")
    
    def stop_server(self):
        """停止服务器"""
        try:
            self.running = False
            self.status_label.config(text="服务器已停止", foreground="red")
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            
            self.add_log("服务器", "服务器已停止")
            
        except Exception as e:
            self.add_log("错误", f"服务器停止失败: {e}")
    
    def run_server(self):
        """运行服务器后台逻辑"""
        while self.running:
            try:
                # 模拟服务器运行
                time.sleep(5)
                if self.running:
                    self.add_log("监控", "系统运行正常")
            except Exception as e:
                self.add_log("错误", f"服务器运行错误: {e}")
                break
    
    def open_settings(self):
        """打开设置窗口"""
        messagebox.showinfo("设置", "系统设置功能正在开发中...")
    
    def open_user_management(self):
        """打开用户管理窗口"""
        messagebox.showinfo("用户管理", "用户管理功能正在开发中...")
    
    def run(self):
        """运行主程序"""
        try:
            # 设置窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 启动GUI主循环
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"程序运行错误: {e}")
            messagebox.showerror("错误", f"程序运行错误: {e}")
    
    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出文件共享系统吗？"):
            if self.running:
                self.stop_server()
            self.root.destroy()

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        print("启动企业级文件共享系统 - 简化版")
        
        # 创建并运行服务器
        server = SimpleFileShareServer()
        server.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
