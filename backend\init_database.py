#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import sys

def create_database():
    """创建数据库"""
    try:
        print("正在连接MySQL服务器...")
        
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        print("MySQL连接成功")
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'file_share_system'")
            result = cursor.fetchone()
            
            if result:
                print("数据库 'file_share_system' 已存在")
            else:
                print("正在创建数据库 'file_share_system'...")
                
                # 创建数据库
                cursor.execute("""
                    CREATE DATABASE file_share_system 
                    CHARACTER SET utf8mb4 
                    COLLATE utf8mb4_unicode_ci
                """)
                
                print("数据库创建成功")
            
            # 切换到数据库
            cursor.execute("USE file_share_system")
            
            # 创建基本表结构
            print("正在创建数据表...")
            
            # 用户表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
                    password_hash VARCHAR(128) NOT NULL COMMENT '密码哈希',
                    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
                    email VARCHAR(100) UNIQUE COMMENT '邮箱',
                    full_name VARCHAR(100) COMMENT '真实姓名',
                    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
                    is_admin BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
                    is_banned BOOLEAN DEFAULT FALSE COMMENT '是否被禁用',
                    ban_until DATETIME COMMENT '禁用到期时间',
                    user_group ENUM('admin', 'user', 'guest') DEFAULT 'user' COMMENT '用户组',
                    last_login DATETIME COMMENT '最后登录时间',
                    login_count INT DEFAULT 0 COMMENT '登录次数',
                    failed_login_attempts INT DEFAULT 0 COMMENT '失败登录次数',
                    last_failed_login DATETIME COMMENT '最后失败登录时间',
                    session_token VARCHAR(128) COMMENT '会话令牌',
                    session_expires DATETIME COMMENT '会话过期时间',
                    download_count INT DEFAULT 0 COMMENT '下载次数',
                    upload_count INT DEFAULT 0 COMMENT '上传次数',
                    search_count INT DEFAULT 0 COMMENT '搜索次数',
                    download_quota INT DEFAULT 0 COMMENT '下载配额(MB)',
                    used_quota INT DEFAULT 0 COMMENT '已用配额(MB)',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    notes TEXT COMMENT '备注'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 共享文件夹表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS shared_folders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL COMMENT '文件夹名称',
                    path TEXT NOT NULL COMMENT '文件夹路径',
                    description TEXT COMMENT '描述',
                    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
                    allow_read BOOLEAN DEFAULT TRUE COMMENT '允许读取',
                    allow_write BOOLEAN DEFAULT FALSE COMMENT '允许写入',
                    allow_delete BOOLEAN DEFAULT FALSE COMMENT '允许删除',
                    allow_upload BOOLEAN DEFAULT FALSE COMMENT '允许上传',
                    allow_download BOOLEAN DEFAULT TRUE COMMENT '允许下载',
                    allow_internal BOOLEAN DEFAULT TRUE COMMENT '允许内网访问',
                    allow_external BOOLEAN DEFAULT FALSE COMMENT '允许外网访问',
                    show_details BOOLEAN DEFAULT TRUE COMMENT '显示详细信息',
                    enable_thumbnail BOOLEAN DEFAULT TRUE COMMENT '启用缩略图',
                    max_file_size BIGINT DEFAULT 0 COMMENT '最大文件大小(字节)',
                    allowed_extensions JSON COMMENT '允许的文件扩展名',
                    file_count INT DEFAULT 0 COMMENT '文件数量',
                    total_size BIGINT DEFAULT 0 COMMENT '总大小(字节)',
                    access_count INT DEFAULT 0 COMMENT '访问次数',
                    download_count INT DEFAULT 0 COMMENT '下载次数',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    last_scanned DATETIME COMMENT '最后扫描时间'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 共享文件表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS shared_files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    folder_id INT NOT NULL,
                    filename VARCHAR(255) NOT NULL COMMENT '文件名',
                    relative_path TEXT NOT NULL COMMENT '相对路径',
                    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
                    file_hash VARCHAR(64) COMMENT '文件哈希值',
                    mime_type VARCHAR(100) COMMENT 'MIME类型',
                    extension VARCHAR(20) COMMENT '文件扩展名',
                    is_image BOOLEAN DEFAULT FALSE COMMENT '是否为图片',
                    is_video BOOLEAN DEFAULT FALSE COMMENT '是否为视频',
                    is_document BOOLEAN DEFAULT FALSE COMMENT '是否为文档',
                    has_thumbnail BOOLEAN DEFAULT FALSE COMMENT '是否有缩略图',
                    thumbnail_path TEXT COMMENT '缩略图路径',
                    image_width INT COMMENT '图片宽度',
                    image_height INT COMMENT '图片高度',
                    image_format VARCHAR(20) COMMENT '图片格式',
                    view_count INT DEFAULT 0 COMMENT '查看次数',
                    download_count INT DEFAULT 0 COMMENT '下载次数',
                    file_modified DATETIME COMMENT '文件修改时间',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                    last_accessed DATETIME COMMENT '最后访问时间',
                    FOREIGN KEY (folder_id) REFERENCES shared_folders(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 活动日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT COMMENT '用户ID',
                    action VARCHAR(100) NOT NULL COMMENT '操作类型',
                    details JSON COMMENT '详细信息',
                    ip_address VARCHAR(45) COMMENT 'IP地址',
                    user_agent TEXT COMMENT '用户代理',
                    success VARCHAR(10) DEFAULT 'success' COMMENT '操作结果',
                    error_message TEXT COMMENT '错误信息',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 权限表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS permissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL COMMENT '权限名称',
                    description TEXT COMMENT '权限描述',
                    permission_type VARCHAR(20) DEFAULT 'file' COMMENT '权限类型',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 用户权限关联表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_permissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    permission_id INT NOT NULL,
                    resource_type VARCHAR(50) COMMENT '资源类型',
                    resource_id INT COMMENT '资源ID',
                    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    expires_at DATETIME COMMENT '过期时间',
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 提交更改
            connection.commit()
            
            print("数据表创建成功")
            
            # 创建默认管理员用户
            print("正在创建默认管理员用户...")
            
            import hashlib
            import secrets
            
            # 生成密码哈希
            password = "admin123"
            salt = secrets.token_hex(16)
            password_hash = hashlib.pbkdf2_hex(
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000,
                64
            )
            
            # 检查管理员是否已存在
            cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if not cursor.fetchone():
                cursor.execute("""
                    INSERT INTO users (username, password_hash, salt, full_name, is_admin, user_group)
                    VALUES ('admin', %s, %s, '系统管理员', TRUE, 'admin')
                """, (password_hash, salt))
                
                connection.commit()
                print("默认管理员用户创建成功")
                print("用户名: admin")
                print("密码: admin123")
            else:
                print("管理员用户已存在")
        
        connection.close()
        print("\n数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def main():
    """主函数"""
    print("企业级文件共享系统 - 数据库初始化")
    print("=" * 50)
    
    if create_database():
        print("\n✓ 数据库初始化成功，现在可以启动服务器了")
        return 0
    else:
        print("\n✗ 数据库初始化失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
