#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from typing import Dict, Any

class SettingsWindow:
    """设置窗口类"""
    
    def __init__(self, parent, server):
        self.parent = parent
        self.server = server
        self.window = None
        self.settings = {}
        
        # 默认设置
        self.default_settings = {
            'server': {
                'host': '0.0.0.0',
                'port': 8080,
                'debug': False,
                'max_connections': 100
            },
            'database': {
                'host': 'localhost',
                'port': 3306,
                'username': 'root',
                'password': '123456',
                'database': 'file_share_system'
            },
            'file': {
                'max_file_size': 100,  # MB
                'allowed_extensions': ['.jpg', '.png', '.pdf', '.doc', '.txt'],
                'thumbnail_size': 200,
                'scan_interval': 300  # 秒
            },
            'security': {
                'session_timeout': 3600,  # 秒
                'max_login_attempts': 5,
                'password_min_length': 6,
                'enable_encryption': True
            },
            'notification': {
                'enable_email': False,
                'smtp_server': '',
                'smtp_port': 587,
                'email_username': '',
                'email_password': ''
            }
        }
        
        self.load_settings()
        self.create_window()
    
    def create_window(self):
        """创建设置窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("系统设置")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 服务器设置标签页
        self.create_server_tab()
        
        # 数据库设置标签页
        self.create_database_tab()
        
        # 文件设置标签页
        self.create_file_tab()
        
        # 安全设置标签页
        self.create_security_tab()
        
        # 通知设置标签页
        self.create_notification_tab()
        
        # 控制按钮
        self.create_control_buttons(main_frame)
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_server_tab(self):
        """创建服务器设置标签页"""
        server_frame = ttk.Frame(self.notebook)
        self.notebook.add(server_frame, text="服务器设置")
        
        # 服务器配置框架
        config_frame = ttk.LabelFrame(server_frame, text="服务器配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 主机地址
        ttk.Label(config_frame, text="主机地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.host_var = tk.StringVar(value=self.settings.get('server', {}).get('host', '0.0.0.0'))
        ttk.Entry(config_frame, textvariable=self.host_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 端口
        ttk.Label(config_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.port_var = tk.StringVar(value=str(self.settings.get('server', {}).get('port', 8080)))
        ttk.Entry(config_frame, textvariable=self.port_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 最大连接数
        ttk.Label(config_frame, text="最大连接数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.max_conn_var = tk.StringVar(value=str(self.settings.get('server', {}).get('max_connections', 100)))
        ttk.Entry(config_frame, textvariable=self.max_conn_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 调试模式
        self.debug_var = tk.BooleanVar(value=self.settings.get('server', {}).get('debug', False))
        ttk.Checkbutton(config_frame, text="启用调试模式", variable=self.debug_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
    
    def create_database_tab(self):
        """创建数据库设置标签页"""
        db_frame = ttk.Frame(self.notebook)
        self.notebook.add(db_frame, text="数据库设置")
        
        # 数据库配置框架
        config_frame = ttk.LabelFrame(db_frame, text="数据库配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 数据库主机
        ttk.Label(config_frame, text="数据库主机:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.db_host_var = tk.StringVar(value=self.settings.get('database', {}).get('host', 'localhost'))
        ttk.Entry(config_frame, textvariable=self.db_host_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 数据库端口
        ttk.Label(config_frame, text="数据库端口:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.db_port_var = tk.StringVar(value=str(self.settings.get('database', {}).get('port', 3306)))
        ttk.Entry(config_frame, textvariable=self.db_port_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 用户名
        ttk.Label(config_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.db_user_var = tk.StringVar(value=self.settings.get('database', {}).get('username', 'root'))
        ttk.Entry(config_frame, textvariable=self.db_user_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 密码
        ttk.Label(config_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.db_pass_var = tk.StringVar(value=self.settings.get('database', {}).get('password', '123456'))
        ttk.Entry(config_frame, textvariable=self.db_pass_var, width=30, show="*").grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 数据库名
        ttk.Label(config_frame, text="数据库名:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.db_name_var = tk.StringVar(value=self.settings.get('database', {}).get('database', 'file_share_system'))
        ttk.Entry(config_frame, textvariable=self.db_name_var, width=30).grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 测试连接按钮
        ttk.Button(config_frame, text="测试连接", command=self.test_database_connection).grid(row=5, column=0, columnspan=2, pady=10)
    
    def create_file_tab(self):
        """创建文件设置标签页"""
        file_frame = ttk.Frame(self.notebook)
        self.notebook.add(file_frame, text="文件设置")
        
        # 文件配置框架
        config_frame = ttk.LabelFrame(file_frame, text="文件配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 最大文件大小
        ttk.Label(config_frame, text="最大文件大小 (MB):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.max_size_var = tk.StringVar(value=str(self.settings.get('file', {}).get('max_file_size', 100)))
        ttk.Entry(config_frame, textvariable=self.max_size_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 缩略图大小
        ttk.Label(config_frame, text="缩略图大小 (像素):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.thumb_size_var = tk.StringVar(value=str(self.settings.get('file', {}).get('thumbnail_size', 200)))
        ttk.Entry(config_frame, textvariable=self.thumb_size_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 扫描间隔
        ttk.Label(config_frame, text="文件扫描间隔 (秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.scan_interval_var = tk.StringVar(value=str(self.settings.get('file', {}).get('scan_interval', 300)))
        ttk.Entry(config_frame, textvariable=self.scan_interval_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 允许的文件扩展名
        ttk.Label(config_frame, text="允许的文件扩展名:").grid(row=3, column=0, sticky=tk.W, pady=5)
        extensions = ', '.join(self.settings.get('file', {}).get('allowed_extensions', ['.jpg', '.png', '.pdf']))
        self.extensions_var = tk.StringVar(value=extensions)
        ttk.Entry(config_frame, textvariable=self.extensions_var, width=50).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
    
    def create_security_tab(self):
        """创建安全设置标签页"""
        security_frame = ttk.Frame(self.notebook)
        self.notebook.add(security_frame, text="安全设置")
        
        # 安全配置框架
        config_frame = ttk.LabelFrame(security_frame, text="安全配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 会话超时
        ttk.Label(config_frame, text="会话超时 (秒):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.session_timeout_var = tk.StringVar(value=str(self.settings.get('security', {}).get('session_timeout', 3600)))
        ttk.Entry(config_frame, textvariable=self.session_timeout_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 最大登录尝试次数
        ttk.Label(config_frame, text="最大登录尝试次数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.max_attempts_var = tk.StringVar(value=str(self.settings.get('security', {}).get('max_login_attempts', 5)))
        ttk.Entry(config_frame, textvariable=self.max_attempts_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 密码最小长度
        ttk.Label(config_frame, text="密码最小长度:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.min_pass_len_var = tk.StringVar(value=str(self.settings.get('security', {}).get('password_min_length', 6)))
        ttk.Entry(config_frame, textvariable=self.min_pass_len_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 启用加密
        self.enable_encryption_var = tk.BooleanVar(value=self.settings.get('security', {}).get('enable_encryption', True))
        ttk.Checkbutton(config_frame, text="启用文件加密", variable=self.enable_encryption_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
    
    def create_notification_tab(self):
        """创建通知设置标签页"""
        notification_frame = ttk.Frame(self.notebook)
        self.notebook.add(notification_frame, text="通知设置")
        
        # 邮件通知配置框架
        email_frame = ttk.LabelFrame(notification_frame, text="邮件通知配置", padding=15)
        email_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 启用邮件通知
        self.enable_email_var = tk.BooleanVar(value=self.settings.get('notification', {}).get('enable_email', False))
        ttk.Checkbutton(email_frame, text="启用邮件通知", variable=self.enable_email_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # SMTP服务器
        ttk.Label(email_frame, text="SMTP服务器:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.smtp_server_var = tk.StringVar(value=self.settings.get('notification', {}).get('smtp_server', ''))
        ttk.Entry(email_frame, textvariable=self.smtp_server_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # SMTP端口
        ttk.Label(email_frame, text="SMTP端口:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.smtp_port_var = tk.StringVar(value=str(self.settings.get('notification', {}).get('smtp_port', 587)))
        ttk.Entry(email_frame, textvariable=self.smtp_port_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 邮箱用户名
        ttk.Label(email_frame, text="邮箱用户名:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_user_var = tk.StringVar(value=self.settings.get('notification', {}).get('email_username', ''))
        ttk.Entry(email_frame, textvariable=self.email_user_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 邮箱密码
        ttk.Label(email_frame, text="邮箱密码:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.email_pass_var = tk.StringVar(value=self.settings.get('notification', {}).get('email_password', ''))
        ttk.Entry(email_frame, textvariable=self.email_pass_var, width=30, show="*").grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置默认", command=self.reset_defaults).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导入设置", command=self.import_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出设置", command=self.export_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=self.on_closing).pack(side=tk.RIGHT)
    
    def load_settings(self):
        """加载设置"""
        try:
            settings_file = os.path.join(os.getcwd(), 'config', 'settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
            else:
                self.settings = self.default_settings.copy()
        except Exception as e:
            print(f"加载设置失败: {e}")
            self.settings = self.default_settings.copy()
    
    def save_settings(self):
        """保存设置"""
        try:
            # 收集所有设置
            new_settings = {
                'server': {
                    'host': self.host_var.get(),
                    'port': int(self.port_var.get()),
                    'debug': self.debug_var.get(),
                    'max_connections': int(self.max_conn_var.get())
                },
                'database': {
                    'host': self.db_host_var.get(),
                    'port': int(self.db_port_var.get()),
                    'username': self.db_user_var.get(),
                    'password': self.db_pass_var.get(),
                    'database': self.db_name_var.get()
                },
                'file': {
                    'max_file_size': int(self.max_size_var.get()),
                    'allowed_extensions': [ext.strip() for ext in self.extensions_var.get().split(',')],
                    'thumbnail_size': int(self.thumb_size_var.get()),
                    'scan_interval': int(self.scan_interval_var.get())
                },
                'security': {
                    'session_timeout': int(self.session_timeout_var.get()),
                    'max_login_attempts': int(self.max_attempts_var.get()),
                    'password_min_length': int(self.min_pass_len_var.get()),
                    'enable_encryption': self.enable_encryption_var.get()
                },
                'notification': {
                    'enable_email': self.enable_email_var.get(),
                    'smtp_server': self.smtp_server_var.get(),
                    'smtp_port': int(self.smtp_port_var.get()),
                    'email_username': self.email_user_var.get(),
                    'email_password': self.email_pass_var.get()
                }
            }
            
            # 保存到文件
            config_dir = os.path.join(os.getcwd(), 'config')
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            settings_file = os.path.join(config_dir, 'settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, indent=4, ensure_ascii=False)
            
            self.settings = new_settings
            messagebox.showinfo("成功", "设置已保存！\n重启服务器后生效。")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def reset_defaults(self):
        """重置为默认设置"""
        result = messagebox.askyesno("确认", "确定要重置为默认设置吗？")
        if result:
            self.settings = self.default_settings.copy()
            self.update_ui_values()
            messagebox.showinfo("成功", "已重置为默认设置")
    
    def update_ui_values(self):
        """更新界面值"""
        # 更新服务器设置
        self.host_var.set(self.settings.get('server', {}).get('host', '0.0.0.0'))
        self.port_var.set(str(self.settings.get('server', {}).get('port', 8080)))
        self.max_conn_var.set(str(self.settings.get('server', {}).get('max_connections', 100)))
        self.debug_var.set(self.settings.get('server', {}).get('debug', False))
        
        # 更新数据库设置
        self.db_host_var.set(self.settings.get('database', {}).get('host', 'localhost'))
        self.db_port_var.set(str(self.settings.get('database', {}).get('port', 3306)))
        self.db_user_var.set(self.settings.get('database', {}).get('username', 'root'))
        self.db_pass_var.set(self.settings.get('database', {}).get('password', '123456'))
        self.db_name_var.set(self.settings.get('database', {}).get('database', 'file_share_system'))
        
        # 更新文件设置
        self.max_size_var.set(str(self.settings.get('file', {}).get('max_file_size', 100)))
        self.thumb_size_var.set(str(self.settings.get('file', {}).get('thumbnail_size', 200)))
        self.scan_interval_var.set(str(self.settings.get('file', {}).get('scan_interval', 300)))
        extensions = ', '.join(self.settings.get('file', {}).get('allowed_extensions', ['.jpg', '.png', '.pdf']))
        self.extensions_var.set(extensions)

        # 更新安全设置
        self.session_timeout_var.set(str(self.settings.get('security', {}).get('session_timeout', 3600)))
        self.max_attempts_var.set(str(self.settings.get('security', {}).get('max_login_attempts', 5)))
        self.min_pass_len_var.set(str(self.settings.get('security', {}).get('password_min_length', 6)))
        self.enable_encryption_var.set(self.settings.get('security', {}).get('enable_encryption', True))

        # 更新通知设置
        self.enable_email_var.set(self.settings.get('notification', {}).get('enable_email', False))
        self.smtp_server_var.set(self.settings.get('notification', {}).get('smtp_server', ''))
        self.smtp_port_var.set(str(self.settings.get('notification', {}).get('smtp_port', 587)))
        self.email_user_var.set(self.settings.get('notification', {}).get('email_username', ''))
        self.email_pass_var.set(self.settings.get('notification', {}).get('email_password', ''))
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            import pymysql
            connection = pymysql.connect(
                host=self.db_host_var.get(),
                port=int(self.db_port_var.get()),
                user=self.db_user_var.get(),
                password=self.db_pass_var.get(),
                database=self.db_name_var.get()
            )
            connection.close()
            messagebox.showinfo("成功", "数据库连接测试成功！")
        except Exception as e:
            messagebox.showerror("错误", f"数据库连接测试失败: {e}")
    
    def import_settings(self):
        """导入设置"""
        file_path = filedialog.askopenfilename(
            title="选择设置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)
                self.settings = imported_settings
                self.update_ui_values()
                messagebox.showinfo("成功", "设置导入成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导入设置失败: {e}")
    
    def export_settings(self):
        """导出设置"""
        file_path = filedialog.asksaveasfilename(
            title="保存设置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", "设置导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导出设置失败: {e}")
    
    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None
