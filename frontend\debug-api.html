<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .auth-status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .auth-ok {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .auth-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API调试工具</h1>
        <p>用于诊断前端API调用问题，检查认证状态和数据加载。</p>

        <div class="section">
            <h3>🔐 认证状态检查</h3>
            <button onclick="checkAuthStatus()">检查认证状态</button>
            <button onclick="clearAuth()">清除认证信息</button>
            <button onclick="simulateLogin()">模拟登录</button>
            
            <div id="auth-status" class="auth-status auth-error">未检查</div>
            <div id="auth-details" class="result">点击"检查认证状态"查看详情</div>
        </div>

        <div class="section">
            <h3>🌐 服务器连接测试</h3>
            <button onclick="testServerConnection()">测试服务器连接</button>
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="testServerStatus()">服务器状态</button>
            
            <div id="server-result" class="result">等待测试...</div>
        </div>

        <div class="section">
            <h3>📁 文件API测试</h3>
            <button onclick="testFileListAPI()">测试文件列表API</button>
            <button onclick="testFolderAPI()">测试文件夹API</button>
            <button onclick="testDirectAPICall()">直接API调用</button>
            
            <div id="file-api-result" class="result">等待测试...</div>
        </div>

        <div class="section">
            <h3>🔍 配置信息</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>API配置</h4>
                    <div id="api-config">加载中...</div>
                </div>
                <div class="info-card">
                    <h4>支持的文件格式</h4>
                    <div id="file-formats">加载中...</div>
                </div>
                <div class="info-card">
                    <h4>调试设置</h4>
                    <div id="debug-settings">加载中...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清除日志</button>
            <button onclick="enableDebugMode()">启用调试模式</button>
            
            <div id="debug-logs" class="result">日志将在这里显示...</div>
        </div>
    </div>

    <!-- 包含必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>

    <script>
        // 页面加载时显示配置信息
        window.addEventListener('load', () => {
            displayConfigInfo();
            checkAuthStatus();
            
            // 重写console.log来捕获日志
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            
            console.log = function(...args) {
                originalLog.apply(console, args);
                addToDebugLog('LOG', args.join(' '));
            };
            
            console.error = function(...args) {
                originalError.apply(console, args);
                addToDebugLog('ERROR', args.join(' '));
            };
            
            console.warn = function(...args) {
                originalWarn.apply(console, args);
                addToDebugLog('WARN', args.join(' '));
            };
        });

        function displayConfigInfo() {
            // API配置
            document.getElementById('api-config').innerHTML = `
                <strong>Base URL:</strong> ${CONFIG.API.BASE_URL}<br>
                <strong>Timeout:</strong> ${CONFIG.API.TIMEOUT}ms<br>
                <strong>Retry Count:</strong> ${CONFIG.API.RETRY_COUNT}<br>
                <strong>Files Endpoint:</strong> ${CONFIG.API.ENDPOINTS.FILES}
            `;

            // 文件格式
            document.getElementById('file-formats').innerHTML = `
                <strong>支持格式:</strong><br>
                ${CONFIG.FILES.ALLOWED_EXTENSIONS.join(', ')}<br>
                <strong>总计:</strong> ${CONFIG.FILES.ALLOWED_EXTENSIONS.length} 种格式
            `;

            // 调试设置
            document.getElementById('debug-settings').innerHTML = `
                <strong>调试模式:</strong> ${CONFIG.DEBUG.ENABLED ? '启用' : '禁用'}<br>
                <strong>日志级别:</strong> ${CONFIG.DEBUG.LOG_LEVEL}<br>
                <strong>显示API调用:</strong> ${CONFIG.DEBUG.SHOW_API_CALLS ? '是' : '否'}
            `;
        }

        function checkAuthStatus() {
            const authData = localStorage.getItem('fileShareAuth');
            const statusDiv = document.getElementById('auth-status');
            const detailsDiv = document.getElementById('auth-details');

            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    statusDiv.className = 'auth-status auth-ok';
                    statusDiv.textContent = '✅ 已登录';
                    
                    detailsDiv.textContent = `认证信息:
用户名: ${auth.user?.username || '未知'}
Token: ${auth.token ? auth.token.substring(0, 20) + '...' : '无'}
登录时间: ${auth.loginTime ? new Date(auth.loginTime).toLocaleString() : '未知'}
权限: ${auth.user?.permissions?.join(', ') || '未知'}`;
                } catch (e) {
                    statusDiv.className = 'auth-status auth-error';
                    statusDiv.textContent = '❌ 认证数据损坏';
                    detailsDiv.textContent = '认证数据格式错误: ' + e.message;
                }
            } else {
                statusDiv.className = 'auth-status auth-error';
                statusDiv.textContent = '❌ 未登录';
                detailsDiv.textContent = '本地存储中没有找到认证信息';
            }
        }

        function clearAuth() {
            localStorage.removeItem('fileShareAuth');
            checkAuthStatus();
            addToDebugLog('INFO', '已清除认证信息');
        }

        function simulateLogin() {
            const mockAuth = {
                token: 'mock-token-' + Date.now(),
                user: {
                    id: 1,
                    username: 'test',
                    permissions: ['read', 'download']
                },
                loginTime: Date.now()
            };
            
            localStorage.setItem('fileShareAuth', JSON.stringify(mockAuth));
            checkAuthStatus();
            addToDebugLog('INFO', '已设置模拟登录信息');
        }

        async function testServerConnection() {
            const result = document.getElementById('server-result');
            result.textContent = '正在测试服务器连接...';

            try {
                const response = await fetch(CONFIG.API.BASE_URL + '/health');
                const data = await response.json();
                
                result.innerHTML = `<span class="status-ok">✅ 服务器连接成功</span>
状态: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                
                addToDebugLog('SUCCESS', '服务器连接测试成功');
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 服务器连接失败</span>
错误: ${error.message}`;
                
                addToDebugLog('ERROR', '服务器连接测试失败: ' + error.message);
            }
        }

        async function testHealthCheck() {
            const result = document.getElementById('server-result');
            result.textContent = '正在进行健康检查...';

            try {
                const response = await api.get('/health');
                result.innerHTML = `<span class="status-ok">✅ 健康检查通过</span>
${JSON.stringify(response.data, null, 2)}`;
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 健康检查失败</span>
${error.message}`;
            }
        }

        async function testServerStatus() {
            const result = document.getElementById('server-result');
            result.textContent = '正在获取服务器状态...';

            try {
                const response = await SystemAPI.getSystemStatus();
                result.innerHTML = `<span class="status-ok">✅ 服务器状态获取成功</span>
${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 服务器状态获取失败</span>
${error.message}`;
            }
        }

        async function testFileListAPI() {
            const result = document.getElementById('file-api-result');
            result.textContent = '正在测试文件列表API...';

            try {
                const response = await FileAPI.getFiles();
                result.innerHTML = `<span class="status-ok">✅ 文件列表API测试成功</span>
响应数据:
${JSON.stringify(response, null, 2)}`;
                
                addToDebugLog('SUCCESS', `文件列表API返回 ${response.files?.length || 0} 个文件`);
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 文件列表API测试失败</span>
错误信息: ${error.message}
用户消息: ${error.userMessage || '无'}`;
                
                addToDebugLog('ERROR', '文件列表API测试失败: ' + error.message);
            }
        }

        async function testFolderAPI() {
            const result = document.getElementById('file-api-result');
            result.textContent = '正在测试文件夹API...';

            try {
                const response = await FolderAPI.getSharedFolders();
                result.innerHTML = `<span class="status-ok">✅ 文件夹API测试成功</span>
${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 文件夹API测试失败</span>
${error.message}`;
            }
        }

        async function testDirectAPICall() {
            const result = document.getElementById('file-api-result');
            result.textContent = '正在进行直接API调用...';

            try {
                const url = CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.FILES;
                const authData = localStorage.getItem('fileShareAuth');
                
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (authData) {
                    const auth = JSON.parse(authData);
                    if (auth.token) {
                        headers['Authorization'] = `Bearer ${auth.token}`;
                    }
                }
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });
                
                const data = await response.json();
                
                result.innerHTML = `<span class="status-ok">✅ 直接API调用成功</span>
URL: ${url}
状态: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                result.innerHTML = `<span class="status-error">❌ 直接API调用失败</span>
${error.message}`;
            }
        }

        function addToDebugLog(level, message) {
            const logsDiv = document.getElementById('debug-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${level}: ${message}\n`;
            
            logsDiv.textContent += logEntry;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('debug-logs').textContent = '日志已清除...\n';
        }

        function enableDebugMode() {
            CONFIG.DEBUG.ENABLED = true;
            CONFIG.DEBUG.SHOW_API_CALLS = true;
            addToDebugLog('INFO', '调试模式已启用');
        }
    </script>
</body>
</html>
