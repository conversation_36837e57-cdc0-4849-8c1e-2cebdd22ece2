#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from typing import Dict, Any, Optional

class FileManagementWindow:
    """文件管理窗口类"""
    
    def __init__(self, parent, server_instance):
        self.parent = parent
        self.server = server_instance
        self.window = None
        self.folders_tree = None
        self.files_tree = None
        self.current_folders = []
        self.current_files = []
        self.selected_folder_id = None
        
    def show(self):
        """显示文件管理窗口"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("文件管理")
        self.window.geometry("1200x800")
        self.window.transient(self.parent)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.create_widgets()
        self.load_shared_folders()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件管理", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="添加共享文件夹", command=self.add_shared_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="删除共享文件夹", command=self.delete_shared_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="文件夹权限", command=self.manage_folder_permissions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="扫描文件", command=self.scan_folder).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分割面板
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：共享文件夹列表
        left_frame = ttk.LabelFrame(paned_window, text="共享文件夹")
        paned_window.add(left_frame, weight=1)
        
        # 共享文件夹树形视图
        folders_columns = ("ID", "名称", "路径", "文件数", "状态")
        self.folders_tree = ttk.Treeview(left_frame, columns=folders_columns, show="headings", height=15)
        
        # 设置列标题和宽度
        folder_widths = {"ID": 50, "名称": 120, "路径": 200, "文件数": 80, "状态": 80}
        
        for col in folders_columns:
            self.folders_tree.heading(col, text=col)
            self.folders_tree.column(col, width=folder_widths.get(col, 100))
        
        # 添加滚动条
        folders_scrollbar_y = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.folders_tree.yview)
        folders_scrollbar_x = ttk.Scrollbar(left_frame, orient=tk.HORIZONTAL, command=self.folders_tree.xview)
        self.folders_tree.configure(yscrollcommand=folders_scrollbar_y.set, xscrollcommand=folders_scrollbar_x.set)
        
        # 布局
        self.folders_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        folders_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        folders_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定选择事件
        self.folders_tree.bind('<<TreeviewSelect>>', self.on_folder_select)
        
        # 右侧：文件列表
        right_frame = ttk.LabelFrame(paned_window, text="文件列表")
        paned_window.add(right_frame, weight=2)
        
        # 文件操作工具栏
        file_toolbar = ttk.Frame(right_frame)
        file_toolbar.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(file_toolbar, text="打开文件夹", command=self.open_folder_location).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_toolbar, text="删除文件", command=self.delete_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_toolbar, text="生成缩略图", command=self.generate_thumbnails).pack(side=tk.LEFT, padx=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(file_toolbar)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind('<Return>', lambda e: self.search_files())
        ttk.Button(search_frame, text="搜索", command=self.search_files).pack(side=tk.LEFT)
        
        # 文件树形视图
        files_columns = ("ID", "文件名", "大小", "类型", "修改时间", "下载次数")
        self.files_tree = ttk.Treeview(right_frame, columns=files_columns, show="headings", height=20)
        
        # 设置列标题和宽度
        file_widths = {"ID": 50, "文件名": 200, "大小": 100, "类型": 80, "修改时间": 150, "下载次数": 80}
        
        for col in files_columns:
            self.files_tree.heading(col, text=col)
            self.files_tree.column(col, width=file_widths.get(col, 100))
        
        # 添加滚动条
        files_scrollbar_y = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        files_scrollbar_x = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.files_tree.xview)
        self.files_tree.configure(yscrollcommand=files_scrollbar_y.set, xscrollcommand=files_scrollbar_x.set)
        
        # 布局
        self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        files_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        files_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定双击事件
        self.files_tree.bind('<Double-1>', lambda e: self.open_file())
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(status_frame, text="文件夹: 0, 文件: 0")
        self.count_label.pack(side=tk.RIGHT)
    
    def load_shared_folders(self):
        """加载共享文件夹列表"""
        try:
            self.status_label.config(text="正在加载共享文件夹...")
            
            # 清空现有数据
            for item in self.folders_tree.get_children():
                self.folders_tree.delete(item)
            
            # 获取文件服务
            file_service = self.server.services.get('file')
            if not file_service:
                self.status_label.config(text="文件服务不可用")
                return
            
            # 获取共享文件夹列表
            result = file_service.get_shared_folders()
            
            if result.get('success', False):
                folders = result.get('folders', [])
                self.current_folders = folders
                
                # 添加文件夹到树形视图
                for folder in folders:
                    status = "正常" if folder.get('is_active', True) else "禁用"
                    
                    self.folders_tree.insert('', 'end', values=(
                        folder.get('id', ''),
                        folder.get('name', ''),
                        folder.get('path', ''),
                        folder.get('file_count', 0),
                        status
                    ))
                
                self.count_label.config(text=f"文件夹: {len(folders)}, 文件: 0")
                self.status_label.config(text="加载完成")
            else:
                self.status_label.config(text=f"加载失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            self.status_label.config(text=f"加载失败: {e}")
            messagebox.showerror("错误", f"加载共享文件夹失败: {e}")
    
    def on_folder_select(self, event):
        """文件夹选择事件"""
        selection = self.folders_tree.selection()
        if not selection:
            return
        
        item = self.folders_tree.item(selection[0])
        folder_id = item['values'][0]
        self.selected_folder_id = folder_id
        
        self.load_files(folder_id)
    
    def load_files(self, folder_id):
        """加载指定文件夹的文件列表"""
        try:
            self.status_label.config(text="正在加载文件...")
            
            # 清空现有数据
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)
            
            # 获取文件服务
            file_service = self.server.services.get('file')
            if not file_service:
                self.status_label.config(text="文件服务不可用")
                return
            
            # 获取文件列表
            result = file_service.get_folder_files(folder_id)
            
            if result.get('success', False):
                files = result.get('files', [])
                self.current_files = files
                
                # 添加文件到树形视图
                for file_info in files:
                    # 正确获取文件大小
                    size = file_info.get('file_size', 0)
                    if size > 1024 * 1024:
                        size_str = f"{size / (1024 * 1024):.1f} MB"
                    elif size > 1024:
                        size_str = f"{size / 1024:.1f} KB"
                    else:
                        size_str = f"{size} B"

                    # 格式化修改时间
                    timestamps = file_info.get('timestamps', {})
                    modified_time = timestamps.get('file_modified', '')
                    if modified_time:
                        modified_time = modified_time[:19]  # 只显示日期时间部分

                    # 获取下载次数
                    statistics = file_info.get('statistics', {})
                    download_count = statistics.get('download_count', 0)

                    self.files_tree.insert('', 'end', values=(
                        file_info.get('id', ''),
                        file_info.get('filename', ''),
                        size_str,
                        file_info.get('extension', ''),
                        modified_time,
                        download_count
                    ))
                
                folder_count = len(self.current_folders)
                self.count_label.config(text=f"文件夹: {folder_count}, 文件: {len(files)}")
                self.status_label.config(text="文件加载完成")
            else:
                self.status_label.config(text=f"加载文件失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            self.status_label.config(text=f"加载文件失败: {e}")
    
    def add_shared_folder(self):
        """添加共享文件夹"""
        try:
            folder_path = filedialog.askdirectory(title="选择要共享的文件夹")
            if folder_path:
                folder_name = os.path.basename(folder_path)
                
                # 获取文件服务
                file_service = self.server.services.get('file')
                if file_service:
                    result = file_service.create_shared_folder(folder_name, folder_path)
                    
                    if result.get('success', False):
                        messagebox.showinfo("成功", f"共享文件夹添加成功: {folder_name}")
                        self.load_shared_folders()
                    else:
                        messagebox.showerror("错误", f"添加共享文件夹失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "文件服务不可用")
        except Exception as e:
            messagebox.showerror("错误", f"添加共享文件夹失败: {e}")
    
    def delete_shared_folder(self):
        """删除共享文件夹"""
        selection = self.folders_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的共享文件夹")
            return
        
        item = self.folders_tree.item(selection[0])
        folder_id = item['values'][0]
        folder_name = item['values'][1]
        
        if messagebox.askyesno("确认", f"确定要删除共享文件夹 '{folder_name}' 吗？\n此操作不会删除实际文件。"):
            try:
                file_service = self.server.services.get('file')
                if file_service:
                    result = file_service.delete_shared_folder(folder_id)
                    
                    if result.get('success', False):
                        messagebox.showinfo("成功", "共享文件夹删除成功")
                        self.load_shared_folders()
                        # 清空文件列表
                        for item in self.files_tree.get_children():
                            self.files_tree.delete(item)
                    else:
                        messagebox.showerror("错误", f"删除共享文件夹失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "文件服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除共享文件夹失败: {e}")
    
    def scan_folder(self):
        """扫描文件夹"""
        if not self.selected_folder_id:
            messagebox.showwarning("警告", "请先选择一个共享文件夹")
            return
        
        try:
            self.status_label.config(text="正在扫描文件夹...")
            
            file_service = self.server.services.get('file')
            if file_service:
                result = file_service.scan_folder(self.selected_folder_id)
                
                if result.get('success', False):
                    scanned_count = result.get('scanned_count', 0)
                    messagebox.showinfo("成功", f"文件夹扫描完成，发现 {scanned_count} 个文件")
                    self.load_files(self.selected_folder_id)
                else:
                    messagebox.showerror("错误", f"扫描文件夹失败: {result.get('error', '未知错误')}")
            else:
                messagebox.showerror("错误", "文件服务不可用")
                
        except Exception as e:
            messagebox.showerror("错误", f"扫描文件夹失败: {e}")
        finally:
            self.status_label.config(text="就绪")
    
    def search_files(self):
        """搜索文件"""
        search_query = self.search_var.get().strip()
        if not search_query:
            if self.selected_folder_id:
                self.load_files(self.selected_folder_id)
            return
        
        try:
            self.status_label.config(text="正在搜索...")
            
            # 在当前文件列表中搜索
            filtered_files = []
            for file_info in self.current_files:
                if search_query.lower() in file_info.get('filename', '').lower():
                    filtered_files.append(file_info)

            # 清空现有数据
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)

            # 添加搜索结果
            for file_info in filtered_files:
                # 正确获取文件大小
                size = file_info.get('file_size', 0)
                if size > 1024 * 1024:
                    size_str = f"{size / (1024 * 1024):.1f} MB"
                elif size > 1024:
                    size_str = f"{size / 1024:.1f} KB"
                else:
                    size_str = f"{size} B"

                # 格式化修改时间
                timestamps = file_info.get('timestamps', {})
                modified_time = timestamps.get('file_modified', '')
                if modified_time:
                    modified_time = modified_time[:19]

                # 获取下载次数
                statistics = file_info.get('statistics', {})
                download_count = statistics.get('download_count', 0)

                self.files_tree.insert('', 'end', values=(
                    file_info.get('id', ''),
                    file_info.get('filename', ''),
                    size_str,
                    file_info.get('extension', ''),
                    modified_time,
                    download_count
                ))
            
            folder_count = len(self.current_folders)
            self.count_label.config(text=f"文件夹: {folder_count}, 搜索结果: {len(filtered_files)}")
            self.status_label.config(text="搜索完成")
            
        except Exception as e:
            self.status_label.config(text=f"搜索失败: {e}")
    
    def open_folder_location(self):
        """打开文件夹位置"""
        if not self.selected_folder_id:
            messagebox.showwarning("警告", "请先选择一个共享文件夹")
            return
        
        try:
            # 找到选中的文件夹
            for folder in self.current_folders:
                if folder.get('id') == self.selected_folder_id:
                    folder_path = folder.get('path', '')
                    if os.path.exists(folder_path):
                        os.startfile(folder_path)  # Windows
                    else:
                        messagebox.showerror("错误", "文件夹路径不存在")
                    break
        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹失败: {e}")
    
    def delete_file(self):
        """删除文件（从数据库中移除，不删除实际文件）"""
        selection = self.files_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的文件")
            return
        
        item = self.files_tree.item(selection[0])
        file_id = item['values'][0]
        file_name = item['values'][1]
        
        if messagebox.askyesno("确认", f"确定要从系统中移除文件 '{file_name}' 吗？\n此操作不会删除实际文件。"):
            try:
                file_service = self.server.services.get('file')
                if file_service:
                    result = file_service.delete_file_record(file_id)
                    
                    if result.get('success', False):
                        messagebox.showinfo("成功", "文件记录删除成功")
                        if self.selected_folder_id:
                            self.load_files(self.selected_folder_id)
                    else:
                        messagebox.showerror("错误", f"删除文件记录失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "文件服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除文件记录失败: {e}")
    
    def generate_thumbnails(self):
        """生成缩略图"""
        if not self.selected_folder_id:
            messagebox.showwarning("警告", "请先选择一个共享文件夹")
            return
        
        try:
            self.status_label.config(text="正在生成缩略图...")
            
            thumbnail_service = self.server.services.get('thumbnail')
            if thumbnail_service:
                # 为当前文件夹的所有图像文件生成缩略图
                image_files = [f for f in self.current_files
                             if f.get('extension', '').lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']]

                generated_count = 0
                for file_info in image_files:
                    # 构建完整文件路径
                    folder_path = ""
                    for folder in self.current_folders:
                        if folder.get('id') == self.selected_folder_id:
                            folder_path = folder.get('path', '')
                            break

                    if folder_path:
                        relative_path = file_info.get('relative_path', '')
                        file_path = os.path.join(folder_path, relative_path)
                        if os.path.exists(file_path):
                            thumbnail_service.generate_thumbnail(file_path)
                            generated_count += 1
                
                messagebox.showinfo("成功", f"为 {generated_count} 个图像文件生成了缩略图")
            else:
                messagebox.showerror("错误", "缩略图服务不可用")
                
        except Exception as e:
            messagebox.showerror("错误", f"生成缩略图失败: {e}")
        finally:
            self.status_label.config(text="就绪")
    
    def open_file(self):
        """打开文件"""
        selection = self.files_tree.selection()
        if not selection:
            return
        
        item = self.files_tree.item(selection[0])
        file_id = item['values'][0]
        
        try:
            # 找到文件信息
            for file_info in self.current_files:
                if file_info.get('id') == file_id:
                    # 构建完整文件路径
                    folder_path = ""
                    for folder in self.current_folders:
                        if folder.get('id') == self.selected_folder_id:
                            folder_path = folder.get('path', '')
                            break

                    if folder_path:
                        relative_path = file_info.get('relative_path', '')
                        file_path = os.path.join(folder_path, relative_path)
                        if os.path.exists(file_path):
                            os.startfile(file_path)  # Windows
                        else:
                            messagebox.showerror("错误", "文件不存在")
                    break
        except Exception as e:
            messagebox.showerror("错误", f"打开文件失败: {e}")
    
    def refresh_all(self):
        """刷新所有数据"""
        self.load_shared_folders()
        if self.selected_folder_id:
            self.load_files(self.selected_folder_id)
    
    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None

    def manage_folder_permissions(self):
        """管理文件夹权限"""
        selection = self.folders_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件夹")
            return

        try:
            # 获取选中的文件夹信息
            item = self.folders_tree.item(selection[0])
            folder_id = item['values'][0]
            folder_name = item['values'][1]

            # 创建权限管理窗口
            self.create_permission_window(folder_id, folder_name)

        except Exception as e:
            messagebox.showerror("错误", f"打开权限管理失败: {e}")

    def create_permission_window(self, folder_id, folder_name):
        """创建权限管理窗口"""
        # 创建新窗口
        perm_window = tk.Toplevel(self.window)
        perm_window.title(f"文件夹权限管理 - {folder_name}")
        perm_window.geometry("600x500")
        perm_window.resizable(True, True)

        # 主框架
        main_frame = ttk.Frame(perm_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = ttk.Label(main_frame, text=f"文件夹: {folder_name} (ID: {folder_id})",
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))

        # 权限设置框架
        perm_frame = ttk.LabelFrame(main_frame, text="权限设置", padding=10)
        perm_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 网络访问权限
        network_frame = ttk.LabelFrame(perm_frame, text="网络访问权限", padding=10)
        network_frame.pack(fill=tk.X, pady=(0, 10))

        # 内网访问
        internal_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(network_frame, text="允许内网访问", variable=internal_var).pack(anchor=tk.W)

        # 外网访问
        external_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(network_frame, text="允许外网访问", variable=external_var).pack(anchor=tk.W)

        # 文件操作权限
        operation_frame = ttk.LabelFrame(perm_frame, text="文件操作权限", padding=10)
        operation_frame.pack(fill=tk.X, pady=(0, 10))

        # 权限变量
        read_var = tk.BooleanVar(value=True)
        write_var = tk.BooleanVar(value=False)
        delete_var = tk.BooleanVar(value=False)
        replace_var = tk.BooleanVar(value=False)
        details_var = tk.BooleanVar(value=True)

        # 权限复选框
        ttk.Checkbutton(operation_frame, text="读取权限 (查看/下载文件)", variable=read_var).pack(anchor=tk.W)
        ttk.Checkbutton(operation_frame, text="写入权限 (上传文件)", variable=write_var).pack(anchor=tk.W)
        ttk.Checkbutton(operation_frame, text="删除权限 (删除文件)", variable=delete_var).pack(anchor=tk.W)
        ttk.Checkbutton(operation_frame, text="替换权限 (覆盖文件)", variable=replace_var).pack(anchor=tk.W)
        ttk.Checkbutton(operation_frame, text="详情权限 (查看文件详细信息)", variable=details_var).pack(anchor=tk.W)

        # 下载限制
        download_frame = ttk.LabelFrame(perm_frame, text="下载限制", padding=10)
        download_frame.pack(fill=tk.X, pady=(0, 10))

        # 单文件大小限制
        size_frame = ttk.Frame(download_frame)
        size_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(size_frame, text="单文件大小限制 (MB):").pack(side=tk.LEFT)
        size_var = tk.StringVar(value="100")
        ttk.Entry(size_frame, textvariable=size_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 批量下载限制
        batch_frame = ttk.Frame(download_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(batch_frame, text="批量下载文件数限制:").pack(side=tk.LEFT)
        batch_var = tk.StringVar(value="10")
        ttk.Entry(batch_frame, textvariable=batch_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 加密设置
        encrypt_frame = ttk.LabelFrame(perm_frame, text="加密下载设置", padding=10)
        encrypt_frame.pack(fill=tk.X, pady=(0, 10))

        # 加密下载次数
        encrypt_count_frame = ttk.Frame(encrypt_frame)
        encrypt_count_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(encrypt_count_frame, text="下载次数超过").pack(side=tk.LEFT)
        encrypt_count_var = tk.StringVar(value="5")
        ttk.Entry(encrypt_count_frame, textvariable=encrypt_count_var, width=5).pack(side=tk.LEFT, padx=(5, 5))
        ttk.Label(encrypt_count_frame, text="次后自动加密").pack(side=tk.LEFT)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def save_permissions():
            """保存权限设置"""
            try:
                # 收集权限数据
                permissions = {
                    'folder_id': folder_id,
                    'network_access': {
                        'internal': internal_var.get(),
                        'external': external_var.get()
                    },
                    'file_operations': {
                        'read': read_var.get(),
                        'write': write_var.get(),
                        'delete': delete_var.get(),
                        'replace': replace_var.get(),
                        'show_details': details_var.get()
                    },
                    'download_limits': {
                        'max_file_size_mb': int(size_var.get()) if size_var.get().isdigit() else 100,
                        'max_batch_files': int(batch_var.get()) if batch_var.get().isdigit() else 10
                    },
                    'encryption': {
                        'encrypt_after_downloads': int(encrypt_count_var.get()) if encrypt_count_var.get().isdigit() else 5
                    }
                }

                # 调用文件服务保存权限设置
                if hasattr(self, 'file_service') and self.file_service:
                    result = self.file_service.set_folder_permissions(folder_id, permissions)
                    if result.get('success', False):
                        messagebox.showinfo("成功", f"文件夹 '{folder_name}' 的权限设置已保存")
                        perm_window.destroy()
                    else:
                        messagebox.showerror("错误", f"保存权限设置失败: {result.get('error', '未知错误')}")
                else:
                    # 暂时显示设置信息
                    messagebox.showinfo("成功", f"文件夹 '{folder_name}' 的权限设置已保存\n\n权限详情:\n" +
                                      f"内网访问: {'是' if internal_var.get() else '否'}\n" +
                                      f"外网访问: {'是' if external_var.get() else '否'}\n" +
                                      f"读取权限: {'是' if read_var.get() else '否'}\n" +
                                      f"写入权限: {'是' if write_var.get() else '否'}\n" +
                                      f"删除权限: {'是' if delete_var.get() else '否'}\n" +
                                      f"文件大小限制: {size_var.get()} MB\n" +
                                      f"批量下载限制: {batch_var.get()} 个文件\n" +
                                      f"加密下载阈值: {encrypt_count_var.get()} 次")
                    perm_window.destroy()

            except Exception as e:
                messagebox.showerror("错误", f"保存权限设置失败: {e}")

        def load_current_permissions():
            """加载当前权限设置"""
            try:
                # 调用文件服务加载当前权限设置
                if hasattr(self, 'file_service') and self.file_service:
                    result = self.file_service.get_folder_permissions(folder_id)
                    if result.get('success', False):
                        perms = result.get('permissions', {})

                        # 设置网络访问权限
                        network = perms.get('network_access', {})
                        internal_var.set(network.get('internal', True))
                        external_var.set(network.get('external', False))

                        # 设置文件操作权限
                        operations = perms.get('file_operations', {})
                        read_var.set(operations.get('read', True))
                        write_var.set(operations.get('write', False))
                        delete_var.set(operations.get('delete', False))
                        replace_var.set(operations.get('replace', False))
                        details_var.set(operations.get('show_details', True))

                        # 设置下载限制
                        limits = perms.get('download_limits', {})
                        size_var.set(str(limits.get('max_file_size_mb', 100)))
                        batch_var.set(str(limits.get('max_batch_files', 10)))

                        # 设置加密设置
                        encryption = perms.get('encryption', {})
                        encrypt_count_var.set(str(encryption.get('encrypt_after_downloads', 5)))

                        messagebox.showinfo("提示", "已加载当前权限设置")
                    else:
                        messagebox.showwarning("警告", f"加载权限设置失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showinfo("提示", "使用默认权限设置")
            except Exception as e:
                messagebox.showerror("错误", f"加载权限设置失败: {e}")

        # 按钮
        ttk.Button(button_frame, text="加载当前设置", command=load_current_permissions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存设置", command=save_permissions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=perm_window.destroy).pack(side=tk.RIGHT)

        # 居中显示窗口
        perm_window.transient(self.window)
        perm_window.grab_set()

        # 自动加载当前权限设置
        perm_window.after(100, load_current_permissions)
