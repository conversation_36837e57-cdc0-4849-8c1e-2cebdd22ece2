#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只启动API服务器，不启动GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.server import APIServer
from config.database import DatabaseManager
from services.file_service import FileService
from services.user_service import UserService
from services.monitoring_service import MonitoringService

def main():
    """主函数"""
    print("🚀 启动API服务器...")
    
    try:
        # 初始化数据库
        print("📊 初始化数据库...")
        db_manager = DatabaseManager()
        db_manager.init_database()
        
        # 初始化服务
        print("🔧 初始化服务...")
        services = {
            'file': FileService(db_manager),
            'user': UserService(db_manager),
            'monitoring': MonitoringService(db_manager)
        }
        
        # 启动API服务器
        print("🌐 启动API服务器...")
        api_server = APIServer(services, port=8086)
        
        print("✅ API服务器启动成功！")
        print("📍 访问地址: http://localhost:8086")
        print("🔧 健康检查: http://localhost:8086/api/health")
        print("📁 文件API: http://localhost:8086/api/files")
        print("\n按 Ctrl+C 停止服务器")
        
        # 运行服务器
        api_server.run()
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
