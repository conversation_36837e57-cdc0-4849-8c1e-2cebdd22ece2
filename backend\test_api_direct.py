#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import DatabaseManager
from services.file_service import FileService
from services.user_service import UserService

def test_file_service():
    """测试文件服务"""
    print("🔧 测试文件服务...")
    
    # 初始化数据库和服务
    db_manager = DatabaseManager()
    db_manager.init_database()
    
    file_service = FileService(db_manager)
    
    print("\n📁 测试获取共享文件夹...")
    folders_result = file_service.get_shared_folders()
    print(f"结果: {folders_result}")
    
    if folders_result.get('success') and folders_result.get('folders'):
        print(f"✅ 找到 {len(folders_result['folders'])} 个共享文件夹")
        
        for folder in folders_result['folders']:
            print(f"   文件夹: {folder['name']} (ID: {folder['id']})")
            
            print(f"\n📄 测试获取文件夹 {folder['id']} 的文件...")
            files_result = file_service.get_root_files(page=1, page_size=10)
            print(f"文件结果: {files_result}")
            
            if files_result.get('success'):
                files = files_result.get('files', [])
                print(f"✅ 找到 {len(files)} 个文件")
                
                # 过滤图片文件
                image_files = []
                for file_item in files:
                    if file_item.get('type') == 'folder':
                        print(f"   📁 文件夹: {file_item.get('name')}")
                    else:
                        filename = file_item.get('filename', file_item.get('name', ''))
                        if filename:
                            ext = filename.lower().split('.')[-1] if '.' in filename else ''
                            allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                            if ext in allowed_exts:
                                image_files.append(file_item)
                                print(f"   🖼️  图片: {filename} ({file_item.get('file_size', 0)} bytes)")
                            else:
                                print(f"   📄 其他: {filename} (扩展名: {ext}) - 已过滤")
                
                print(f"\n✅ 过滤后的图片文件数量: {len(image_files)}")
                
            else:
                print(f"❌ 获取文件失败: {files_result.get('error')}")
            
            break  # 只测试第一个文件夹
    else:
        print(f"❌ 获取共享文件夹失败: {folders_result.get('error')}")

def test_user_service():
    """测试用户服务"""
    print("\n👤 测试用户服务...")
    
    db_manager = DatabaseManager()
    db_manager.init_database()
    
    user_service = UserService(db_manager)
    
    # 测试用户认证
    print("🔐 测试用户认证...")
    auth_result = user_service.authenticate_user('test', 'test123', '127.0.0.1', 'Test-Agent')
    print(f"认证结果: {auth_result}")
    
    if auth_result.get('success'):
        print("✅ 用户认证成功")
        token = auth_result.get('session_token')
        
        # 测试token验证
        print("🔍 测试token验证...")
        validate_result = user_service.validate_session(token)
        print(f"验证结果: {validate_result}")
        
        if validate_result:
            print("✅ Token验证成功")
        else:
            print("❌ Token验证失败")
    else:
        print(f"❌ 用户认证失败: {auth_result.get('error')}")

def main():
    """主函数"""
    print("🚀 开始API直接测试...")
    print("=" * 50)
    
    try:
        test_user_service()
        test_file_service()
        
        print("\n" + "=" * 50)
        print("✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
