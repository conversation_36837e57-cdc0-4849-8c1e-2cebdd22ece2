<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>登录修复测试</h1>
        
        <div class="test-step">
            <h3>步骤1: 模拟登录</h3>
            <div id="step1Status">等待测试...</div>
            <button onclick="testLogin()">测试登录</button>
        </div>
        
        <div class="test-step">
            <h3>步骤2: 验证Token</h3>
            <div id="step2Status">等待测试...</div>
            <button onclick="testTokenVerify()">验证Token</button>
        </div>
        
        <div class="test-step">
            <h3>步骤3: 测试主页面认证</h3>
            <div id="step3Status">等待测试...</div>
            <button onclick="testMainPageAuth()">测试主页认证</button>
        </div>
        
        <div class="test-step">
            <h3>步骤4: 完整流程测试</h3>
            <div id="step4Status">等待测试...</div>
            <button onclick="testFullFlow()">完整流程测试</button>
        </div>
        
        <div class="test-step">
            <h3>测试日志</h3>
            <pre id="testLog">等待测试开始...</pre>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        const logs = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            document.getElementById('testLog').textContent = logs.join('\n');
        }
        
        function clearLog() {
            logs.length = 0;
            document.getElementById('testLog').textContent = '日志已清除';
        }
        
        async function testLogin() {
            const statusDiv = document.getElementById('step1Status');
            statusDiv.innerHTML = '测试中...';
            
            try {
                log('开始登录测试...');
                
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test123'
                    })
                });
                
                const result = await response.json();
                log(`登录响应: ${JSON.stringify(result, null, 2)}`);
                
                if (response.ok && result.success) {
                    // 保存登录信息
                    const authInfo = {
                        token: result.token,
                        user: result.user,
                        serverUrl: 'http://localhost:8086',
                        loginTime: Date.now()
                    };
                    
                    localStorage.setItem('fileShareAuth', JSON.stringify(authInfo));
                    
                    statusDiv.innerHTML = '<span class="status-ok">✅ 登录成功</span>';
                    log('登录成功，已保存认证信息');
                } else {
                    statusDiv.innerHTML = '<span class="status-error">❌ 登录失败</span>';
                    log(`登录失败: ${result.error || '未知错误'}`);
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-error">❌ 登录请求失败</span>';
                log(`登录请求异常: ${error.message}`);
            }
        }
        
        async function testTokenVerify() {
            const statusDiv = document.getElementById('step2Status');
            statusDiv.innerHTML = '测试中...';
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    statusDiv.innerHTML = '<span class="status-error">❌ 未找到登录信息</span>';
                    log('Token验证失败: 未找到登录信息');
                    return;
                }
                
                const auth = JSON.parse(authData);
                log('开始Token验证...');
                
                const response = await fetch('http://localhost:8086/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${auth.token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                log(`Token验证响应: ${JSON.stringify(result, null, 2)}`);
                
                if (response.ok && result.valid) {
                    statusDiv.innerHTML = '<span class="status-ok">✅ Token验证成功</span>';
                    log('Token验证成功');
                } else {
                    statusDiv.innerHTML = '<span class="status-error">❌ Token验证失败</span>';
                    log(`Token验证失败: ${result.error || '未知错误'}`);
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-error">❌ Token验证请求失败</span>';
                log(`Token验证异常: ${error.message}`);
            }
        }
        
        async function testMainPageAuth() {
            const statusDiv = document.getElementById('step3Status');
            statusDiv.innerHTML = '测试中...';
            
            try {
                log('开始主页认证测试...');
                
                // 模拟主页面的认证检查逻辑
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    statusDiv.innerHTML = '<span class="status-error">❌ 未找到登录信息</span>';
                    log('主页认证失败: 未找到登录信息');
                    return;
                }
                
                const auth = JSON.parse(authData);
                
                // 验证token
                const response = await fetch(`${auth.serverUrl}/api/auth/verify`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${auth.token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.valid) {
                        statusDiv.innerHTML = '<span class="status-ok">✅ 主页认证成功</span>';
                        log('主页认证成功，可以正常访问主界面');
                    } else {
                        statusDiv.innerHTML = '<span class="status-error">❌ 主页认证失败</span>';
                        log(`主页认证失败: ${result.error}`);
                    }
                } else {
                    statusDiv.innerHTML = '<span class="status-error">❌ 主页认证请求失败</span>';
                    log(`主页认证请求失败: ${response.status}`);
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-error">❌ 主页认证异常</span>';
                log(`主页认证异常: ${error.message}`);
            }
        }
        
        async function testFullFlow() {
            const statusDiv = document.getElementById('step4Status');
            statusDiv.innerHTML = '测试中...';
            
            log('开始完整流程测试...');
            
            // 清除现有登录信息
            localStorage.removeItem('fileShareAuth');
            log('已清除现有登录信息');
            
            // 步骤1: 登录
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 步骤2: 验证token
            await testTokenVerify();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 步骤3: 测试主页认证
            await testMainPageAuth();
            
            statusDiv.innerHTML = '<span class="status-ok">✅ 完整流程测试完成</span>';
            log('完整流程测试完成');
        }
        
        // 页面加载时检查当前状态
        window.addEventListener('load', () => {
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                log('发现现有登录信息');
                try {
                    const auth = JSON.parse(authData);
                    log(`当前用户: ${auth.user?.username || '未知'}`);
                    log(`服务器: ${auth.serverUrl || '未知'}`);
                    log(`登录时间: ${new Date(auth.loginTime).toLocaleString()}`);
                } catch (e) {
                    log('登录信息解析失败');
                }
            } else {
                log('未找到登录信息');
            }
        });
    </script>
</body>
</html>
