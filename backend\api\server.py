#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器
"""

from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import threading
import time
from datetime import datetime
from typing import Dict, Any

from utils.logger import setup_logger

class APIServer:
    """API服务器类"""
    
    def __init__(self, services: Dict[str, Any], settings):
        self.services = services
        self.settings = settings
        self.logger = setup_logger("APIServer")
        
        # 创建Flask应用
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'
        
        # 启用CORS
        CORS(self.app)
        
        # 启用SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 注册路由
        self.register_routes()
        self.register_socketio_events()
        
        self.running = False
    
    def register_routes(self):
        """注册API路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'ok',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/server/status', methods=['GET'])
        def server_status():
            """获取服务器状态"""
            return jsonify({
                'running': self.running,
                'uptime': time.time() - getattr(self, 'start_time', time.time()),
                'services': list(self.services.keys())
            })
        
        @self.app.route('/api/auth/login', methods=['POST'])
        def login():
            """用户登录"""
            try:
                data = request.get_json()
                username = data.get('username')
                password = data.get('password')
                
                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400
                
                # 这里应该调用用户服务进行验证
                # user_service = self.services.get('user')
                # result = user_service.authenticate(username, password)
                
                # 临时返回成功响应
                return jsonify({
                    'success': True,
                    'token': 'temp-token',
                    'user': {
                        'id': 1,
                        'username': username,
                        'permissions': ['read', 'download']
                    }
                })
                
            except Exception as e:
                self.logger.error(f"登录失败: {e}")
                return jsonify({'error': '登录失败'}), 500
        
        @self.app.route('/api/files/folders', methods=['GET'])
        def get_shared_folders():
            """获取共享文件夹列表"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503
                
                folders = file_service.get_shared_folders()
                return jsonify({'folders': folders})
                
            except Exception as e:
                self.logger.error(f"获取文件夹列表失败: {e}")
                return jsonify({'error': '获取文件夹列表失败'}), 500
        
        @self.app.route('/api/files/folders/<int:folder_id>/files', methods=['GET'])
        def get_folder_files(folder_id):
            """获取文件夹中的文件"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503
                
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')
                
                result = file_service.get_folder_files(
                    folder_id, page, page_size, search_query
                )
                
                return jsonify(result)
                
            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500
        
        @self.app.route('/api/files/<int:file_id>/download', methods=['GET'])
        def download_file(file_id):
            """下载文件"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    abort(503)
                
                file_info = file_service.get_file_info(file_id)
                if not file_info:
                    abort(404)
                
                # 这里应该检查用户权限
                # 这里应该记录下载日志
                
                # 返回文件
                # return send_file(file_info['full_path'], as_attachment=True)
                
                # 临时返回文件信息
                return jsonify(file_info)
                
            except Exception as e:
                self.logger.error(f"下载文件失败: {e}")
                abort(500)
        
        @self.app.route('/api/search', methods=['POST'])
        def search_files():
            """搜索文件"""
            try:
                data = request.get_json()
                query = data.get('query', '')
                search_type = data.get('type', 'text')  # text 或 image
                
                search_service = self.services.get('search')
                if not search_service:
                    return jsonify({'error': '搜索服务不可用'}), 503
                
                # 这里应该调用搜索服务
                # results = search_service.search(query, search_type)
                
                # 临时返回空结果
                return jsonify({
                    'results': [],
                    'total': 0,
                    'query': query,
                    'search_type': search_type
                })
                
            except Exception as e:
                self.logger.error(f"搜索失败: {e}")
                return jsonify({'error': '搜索失败'}), 500
        
        @self.app.route('/api/admin/users', methods=['GET'])
        def get_users():
            """获取用户列表（管理员）"""
            try:
                # 这里应该检查管理员权限
                
                # 临时返回空列表
                return jsonify({'users': []})
                
            except Exception as e:
                self.logger.error(f"获取用户列表失败: {e}")
                return jsonify({'error': '获取用户列表失败'}), 500
        
        @self.app.route('/api/admin/stats', methods=['GET'])
        def get_statistics():
            """获取系统统计信息"""
            try:
                # 这里应该收集各种统计信息
                stats = {
                    'total_users': 0,
                    'online_users': 0,
                    'total_files': 0,
                    'total_size': 0,
                    'today_downloads': 0,
                    'today_uploads': 0,
                    'today_searches': 0
                }
                
                return jsonify(stats)
                
            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                return jsonify({'error': '获取统计信息失败'}), 500
    
    def register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.logger.info(f"客户端连接: {request.sid}")
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.logger.info(f"客户端断开连接: {request.sid}")
        
        @self.socketio.on('ping')
        def handle_ping():
            """心跳检测"""
            emit('pong', {'timestamp': datetime.now().isoformat()})
    
    def broadcast_notification(self, message: str, notification_type: str = "info"):
        """广播通知"""
        try:
            self.socketio.emit('notification', {
                'message': message,
                'type': notification_type,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"广播通知失败: {e}")
    
    def broadcast_user_activity(self, activity_data: Dict[str, Any]):
        """广播用户活动"""
        try:
            self.socketio.emit('user_activity', activity_data)
        except Exception as e:
            self.logger.error(f"广播用户活动失败: {e}")
    
    def run(self):
        """运行服务器"""
        try:
            self.running = True
            self.start_time = time.time()
            
            host = self.settings.get('server.host', '0.0.0.0')
            port = self.settings.get('server.port', 8080)
            debug = self.settings.get('server.debug', False)
            
            self.logger.info(f"API服务器启动: {host}:{port}")
            
            self.socketio.run(
                self.app,
                host=host,
                port=port,
                debug=debug,
                use_reloader=False
            )
            
        except Exception as e:
            self.logger.error(f"API服务器运行失败: {e}")
            self.running = False
    
    def stop(self):
        """停止服务器"""
        try:
            self.running = False
            self.logger.info("API服务器已停止")
        except Exception as e:
            self.logger.error(f"停止API服务器失败: {e}")
    
    def get_app(self):
        """获取Flask应用实例"""
        return self.app
    
    def get_socketio(self):
        """获取SocketIO实例"""
        return self.socketio
