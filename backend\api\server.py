#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器
"""

from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import threading
import time
import os
from datetime import datetime
from typing import Dict, Any, Optional

from utils.logger import setup_logger

# 可选导入服务模块
try:
    from services.user_service import UserService
    HAS_USER_SERVICE = True
except ImportError:
    HAS_USER_SERVICE = False

try:
    from services.thumbnail_service import ThumbnailService
    HAS_THUMBNAIL_SERVICE = True
except ImportError:
    HAS_THUMBNAIL_SERVICE = False

try:
    from services.encryption_service import EncryptionService
    HAS_ENCRYPTION_SERVICE = True
except ImportError:
    HAS_ENCRYPTION_SERVICE = False

class APIServer:
    """API服务器类"""

    def __init__(self, services: Dict[str, Any], settings):
        self.services = services
        self.settings = settings
        self.logger = setup_logger("APIServer")

        # 初始化额外服务
        self._initialize_additional_services()

        # 创建Flask应用
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'

        # 启用CORS
        try:
            CORS(self.app)
        except:
            self.logger.warning("CORS模块不可用")

        # 启用SocketIO
        try:
            self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        except:
            self.logger.warning("SocketIO模块不可用")
            self.socketio = None

        # 注册路由
        self.register_routes()
        if self.socketio:
            self.register_socketio_events()

        self.running = False

    def _initialize_additional_services(self):
        """初始化额外服务"""
        try:
            # 初始化用户服务
            if HAS_USER_SERVICE and 'user' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['user'] = UserService(db_manager)
                self.logger.info("用户服务初始化成功")

            # 初始化缩略图服务
            if HAS_THUMBNAIL_SERVICE and 'thumbnail' not in self.services:
                self.services['thumbnail'] = ThumbnailService()
                self.logger.info("缩略图服务初始化成功")

            # 初始化加密服务
            if HAS_ENCRYPTION_SERVICE and 'encryption' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['encryption'] = EncryptionService(db_manager=db_manager)
                self.logger.info("加密服务初始化成功")

        except Exception as e:
            self.logger.error(f"初始化额外服务失败: {e}")
    
    def register_routes(self):
        """注册API路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'ok',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/server/status', methods=['GET'])
        def server_status():
            """获取服务器状态"""
            return jsonify({
                'running': self.running,
                'uptime': time.time() - getattr(self, 'start_time', time.time()),
                'services': list(self.services.keys())
            })
        
        @self.app.route('/api/auth/login', methods=['POST'])
        def login():
            """用户登录"""
            try:
                data = request.get_json()
                username = data.get('username')
                password = data.get('password')

                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400

                # 获取客户端信息
                ip_address = request.remote_addr
                user_agent = request.headers.get('User-Agent')

                # 使用用户服务进行验证
                user_service = self.services.get('user')
                if user_service:
                    result = user_service.authenticate_user(
                        username, password, ip_address, user_agent
                    )

                    if result['success']:
                        # 记录用户登录活动到监控服务
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                result['user']['id'],
                                'login',
                                {'username': username, 'success': True},
                                ip_address
                            )

                        return jsonify({
                            'success': True,
                            'token': result['session_token'],
                            'user': result['user']
                        })
                    else:
                        # 记录登录失败
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                0,  # 未知用户ID
                                'login_failed',
                                {'username': username, 'error': result['error']},
                                ip_address
                            )
                        return jsonify({'error': result['error']}), 401
                else:
                    # 临时返回成功响应（用户服务不可用时）
                    return jsonify({
                        'success': True,
                        'token': 'temp-token',
                        'user': {
                            'id': 1,
                            'username': username,
                            'permissions': ['read', 'download']
                        }
                    })

            except Exception as e:
                self.logger.error(f"登录失败: {e}")
                return jsonify({'error': '登录失败'}), 500

        @self.app.route('/api/auth/logout', methods=['POST'])
        def logout():
            """用户登出"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                ip_address = request.remote_addr

                # 获取用户信息用于记录
                user_info = self._validate_token(token)

                user_service = self.services.get('user')
                if user_service and token:
                    user_service.logout_user(token)

                # 记录登出活动
                if user_info:
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_info.get('user_id'),
                            'logout',
                            {'username': user_info.get('username')},
                            ip_address
                        )

                return jsonify({'success': True})

            except Exception as e:
                self.logger.error(f"登出失败: {e}")
                return jsonify({'error': '登出失败'}), 500

        @self.app.route('/api/auth/verify', methods=['GET'])
        def verify_token():
            """验证token有效性"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')

                if not token:
                    return jsonify({'valid': False, 'error': '缺少token'}), 401

                user_info = self._validate_token(token)
                if user_info:
                    return jsonify({
                        'valid': True,
                        'user': {
                            'id': user_info.get('user_id'),
                            'username': user_info.get('username'),
                            'permissions': user_info.get('permissions', []),
                            'is_admin': user_info.get('is_admin', False)
                        }
                    })
                else:
                    return jsonify({'valid': False, 'error': 'token无效或已过期'}), 401

            except Exception as e:
                self.logger.error(f"token验证失败: {e}")
                return jsonify({'valid': False, 'error': 'token验证失败'}), 500
        
        @self.app.route('/api/files/folders', methods=['GET'])
        def get_shared_folders():
            """获取共享文件夹列表"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503
                
                folders = file_service.get_shared_folders()
                return jsonify({'folders': folders})
                
            except Exception as e:
                self.logger.error(f"获取文件夹列表失败: {e}")
                return jsonify({'error': '获取文件夹列表失败'}), 500
        
        @self.app.route('/api/files/folders/<int:folder_id>/files', methods=['GET'])
        def get_folder_files(folder_id):
            """获取文件夹中的文件"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503
                
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')
                
                result = file_service.get_folder_files(
                    folder_id, page, page_size, search_query
                )
                
                return jsonify(result)
                
            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500
        
        @self.app.route('/api/files/<int:file_id>/download', methods=['GET'])
        def download_file(file_id):
            """下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                if not file_info:
                    abort(404)

                # 检查文件是否存在
                if not file_info.get('exists', False):
                    abort(404)

                # 使用加密服务处理下载
                encryption_service = self.services.get('encryption')
                if encryption_service:
                    result = encryption_service.create_single_file_package(
                        file_info['full_path'], user_info['user_id']
                    )

                    if result['success']:
                        if result.get('encrypted', False):
                            # 返回加密包信息
                            return jsonify({
                                'encrypted': True,
                                'package_id': os.path.basename(result['package_path']),
                                'message': '文件已加密，请申请解压密码'
                            })
                        else:
                            # 返回原文件
                            try:
                                return send_file(result['package_path'], as_attachment=True)
                            except:
                                return jsonify(file_info)
                    else:
                        return jsonify({'error': result['error']}), 500
                else:
                    # 直接返回文件信息（加密服务不可用时）
                    return jsonify(file_info)

            except Exception as e:
                self.logger.error(f"下载文件失败: {e}")
                abort(500)

        @self.app.route('/api/files/batch/download', methods=['POST'])
        def batch_download():
            """批量下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                # 获取文件路径
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                file_paths = []
                for file_id in file_ids:
                    file_info = file_service.get_file_info(file_id)
                    if file_info and file_info.get('exists', False):
                        file_paths.append(file_info['full_path'])

                if not file_paths:
                    return jsonify({'error': '没有有效的文件'}), 400

                # 创建批量下载包
                encryption_service = self.services.get('encryption')
                if encryption_service:
                    result = encryption_service.create_batch_package(
                        file_paths, user_info['user_id']
                    )

                    if result['success']:
                        if result.get('encrypted', False):
                            return jsonify({
                                'success': True,
                                'encrypted': True,
                                'package_id': os.path.basename(result['package_path']),
                                'file_count': result['file_count'],
                                'message': '文件包已加密，请申请解压密码'
                            })
                        else:
                            return jsonify({
                                'success': True,
                                'encrypted': False,
                                'download_url': f"/api/files/package/{os.path.basename(result['package_path'])}",
                                'file_count': result['file_count']
                            })
                    else:
                        return jsonify({'error': result['error']}), 500
                else:
                    return jsonify({'error': '加密服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '批量下载失败'}), 500
        
        @self.app.route('/api/search', methods=['POST'])
        def search_files():
            """搜索文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                query = data.get('query', '')
                search_type = data.get('type', 'text')  # text 或 image
                ip_address = request.remote_addr

                # 记录搜索活动
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_service.record_user_activity(
                        user_info.get('user_id'),
                        'search',
                        {'query': query, 'search_type': search_type},
                        ip_address
                    )

                search_service = self.services.get('search')
                if not search_service:
                    return jsonify({'error': '搜索服务不可用'}), 503

                # 这里应该调用搜索服务
                # results = search_service.search(query, search_type)

                # 临时返回空结果
                return jsonify({
                    'results': [],
                    'total': 0,
                    'query': query,
                    'search_type': search_type
                })

            except Exception as e:
                self.logger.error(f"搜索失败: {e}")
                return jsonify({'error': '搜索失败'}), 500

        # 新的下载接口
        @self.app.route('/api/download/single/<int:file_id>', methods=['POST'])
        def download_single_file(file_id):
            """单文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                ip_address = request.remote_addr
                result = download_service.prepare_single_file_download(file_id, user_id)

                if result.get('success'):
                    # 记录下载活动
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'download',
                            {
                                'file_id': file_id,
                                'filename': result.get('filename'),
                                'file_size': result.get('file_size'),
                                'is_encrypted': result.get('is_encrypted')
                            },
                            ip_address
                        )

                    return jsonify({
                        'success': True,
                        'data': {
                            'download_id': result.get('file_id'),
                            'filename': result.get('filename'),
                            'file_size': result.get('file_size'),
                            'is_encrypted': result.get('is_encrypted'),
                            'download_url': f"/api/download/file/{os.path.basename(result.get('zip_path'))}",
                            'password_hint': '如需解压密码，请使用密码申请接口' if result.get('is_encrypted') else None
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"单文件下载失败: {e}")
                return jsonify({'error': '下载失败'}), 500

        @self.app.route('/api/download/batch', methods=['POST'])
        def download_batch_files():
            """批量文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                result = download_service.prepare_batch_download(file_ids, user_id)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'data': {
                            'file_count': result.get('file_count'),
                            'file_size': result.get('file_size'),
                            'is_encrypted': result.get('is_encrypted'),
                            'download_url': f"/api/download/file/{os.path.basename(result.get('zip_path'))}",
                            'encrypted_files': result.get('encrypted_file_ids', []),
                            'password_hint': '如需解压密码，请使用密码申请接口' if result.get('is_encrypted') else None
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '批量下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '批量下载失败'}), 500

        @self.app.route('/api/download/file/<path:filename>', methods=['GET'])
        def serve_download_file(filename):
            """提供下载文件服务"""
            try:
                download_service = self.services.get('download')
                if not download_service:
                    abort(503)

                file_path = os.path.join(download_service.temp_dir, filename)
                if not os.path.exists(file_path):
                    abort(404)

                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/zip'
                )

            except Exception as e:
                self.logger.error(f"文件下载服务失败: {e}")
                abort(500)

        @self.app.route('/api/download/password/request', methods=['POST'])
        def request_download_password():
            """申请下载密码"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_id = data.get('file_id')
                reason = data.get('reason', '')

                if not file_id:
                    return jsonify({'error': '文件ID不能为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                result = download_service.request_password(file_id, user_id)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'data': {
                            'password': result.get('password'),
                            'remaining_requests': result.get('remaining_requests'),
                            'message': '密码申请成功'
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '密码申请失败')}), 400

            except Exception as e:
                self.logger.error(f"密码申请失败: {e}")
                return jsonify({'error': '密码申请失败'}), 500

        @self.app.route('/api/download/records', methods=['GET'])
        def get_download_records():
            """获取下载记录"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 这里应该从数据库获取下载记录
                # 暂时返回空列表
                return jsonify({
                    'success': True,
                    'data': {
                        'records': [],
                        'total': 0
                    }
                })

            except Exception as e:
                self.logger.error(f"获取下载记录失败: {e}")
                return jsonify({'error': '获取下载记录失败'}), 500
        
        @self.app.route('/api/admin/users', methods=['GET'])
        def get_users():
            """获取用户列表（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    page = request.args.get('page', 1, type=int)
                    page_size = request.args.get('page_size', 20, type=int)
                    search_query = request.args.get('search', '')

                    result = user_service.get_user_list(page, page_size, search_query)
                    return jsonify(result)
                else:
                    return jsonify({'users': [], 'total_count': 0})

            except Exception as e:
                self.logger.error(f"获取用户列表失败: {e}")
                return jsonify({'error': '获取用户列表失败'}), 500

        @self.app.route('/api/admin/users', methods=['POST'])
        def create_user():
            """创建用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                username = data.get('username')
                password = data.get('password')
                email = data.get('email')
                full_name = data.get('full_name')
                user_group = data.get('user_group', 'user')
                is_admin = data.get('is_admin', False)

                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.create_user(
                        username, password, email, full_name, user_group, is_admin
                    )
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"创建用户失败: {e}")
                return jsonify({'error': '创建用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
        def update_user(user_id):
            """更新用户信息（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.update_user(user_id, data)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"更新用户失败: {e}")
                return jsonify({'error': '更新用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
        def delete_user(user_id):
            """删除用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.delete_user(user_id)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"删除用户失败: {e}")
                return jsonify({'error': '删除用户失败'}), 500
        
        @self.app.route('/api/admin/stats', methods=['GET'])
        def get_statistics():
            """获取系统统计信息"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 收集各种统计信息
                stats = {
                    'total_users': 0,
                    'online_users': 0,
                    'total_files': 0,
                    'total_size': 0,
                    'today_downloads': 0,
                    'today_uploads': 0,
                    'today_searches': 0
                }

                # 获取用户统计
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    stats.update({
                        'total_users': user_stats.get('total_users', 0),
                        'online_users': user_stats.get('online_users', 0),
                        'active_users': user_stats.get('active_users', 0),
                        'admin_users': user_stats.get('admin_users', 0)
                    })

                # 获取监控统计
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_stats = monitoring_service.get_activity_statistics()
                    stats.update({
                        'total_activities': monitoring_stats.get('total_activities', 0),
                        'recent_activities_24h': monitoring_stats.get('recent_activities_24h', 0),
                        'activity_types': monitoring_stats.get('activity_types', {})
                    })

                # 获取文件统计
                file_service = self.services.get('file')
                if file_service:
                    # TODO: 实现文件统计
                    pass

                return jsonify({
                    'success': True,
                    'data': stats
                })

            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                return jsonify({'error': '获取统计信息失败'}), 500

        @self.app.route('/api/system/info', methods=['GET'])
        def get_system_info():
            """获取系统信息（普通用户可访问）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 基础系统信息
                info = {
                    'version': '1.0.0',
                    'uptime': time.time() - getattr(self, 'start_time', time.time()),
                    'services_count': len(self.services),
                    'status': 'running' if self.running else 'stopped'
                }

                # 获取在线用户数（不显示具体用户信息）
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    info['online_users'] = user_stats.get('online_users', 0)

                # 获取存储信息
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    system_status = monitoring_service.get_system_status()
                    system_stats = system_status.get('system_stats', {})
                    if 'disk' in system_stats:
                        disk_info = system_stats['disk']
                        info['storage'] = {
                            'total': disk_info.get('total', 0),
                            'used': disk_info.get('used', 0),
                            'free': disk_info.get('free', 0),
                            'percent': disk_info.get('percent', 0)
                        }

                return jsonify({
                    'success': True,
                    'data': info
                })

            except Exception as e:
                self.logger.error(f"获取系统信息失败: {e}")
                return jsonify({'error': '获取系统信息失败'}), 500

        @self.app.route('/api/system/online-users', methods=['GET'])
        def get_online_users():
            """获取在线用户列表"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 获取在线用户
                user_service = self.services.get('user')
                if user_service:
                    online_users = user_service.get_online_users()
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': online_users,
                            'count': len(online_users)
                        }
                    })
                else:
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': [],
                            'count': 0
                        }
                    })

            except Exception as e:
                self.logger.error(f"获取在线用户失败: {e}")
                return jsonify({'error': '获取在线用户失败'}), 500
    
    def register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.logger.info(f"客户端连接: {request.sid}")
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.logger.info(f"客户端断开连接: {request.sid}")
        
        @self.socketio.on('ping')
        def handle_ping():
            """心跳检测"""
            emit('pong', {'timestamp': datetime.now().isoformat()})
    
    def broadcast_notification(self, message: str, notification_type: str = "info"):
        """广播通知"""
        try:
            self.socketio.emit('notification', {
                'message': message,
                'type': notification_type,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"广播通知失败: {e}")
    
    def broadcast_user_activity(self, activity_data: Dict[str, Any]):
        """广播用户活动"""
        try:
            if self.socketio:
                self.socketio.emit('user_activity', activity_data)
        except Exception as e:
            self.logger.error(f"广播用户活动失败: {e}")

    def _validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证用户token"""
        try:
            if not token:
                return None

            user_service = self.services.get('user')
            if user_service:
                return user_service.validate_session(token)
            else:
                # 用户服务不可用时，返回临时用户信息
                return {
                    'user_id': 1,
                    'username': 'temp_user',
                    'permissions': ['read', 'download'],
                    'is_admin': True  # 临时给予管理员权限用于测试
                }

        except Exception as e:
            self.logger.error(f"验证token失败: {e}")
            return None

    def _check_permission(self, user_info: Dict[str, Any], permission: str) -> bool:
        """检查用户权限"""
        try:
            if not user_info:
                return False

            permissions = user_info.get('permissions', [])
            return permission in permissions or user_info.get('is_admin', False)

        except Exception as e:
            self.logger.error(f"检查权限失败: {e}")
            return False
    
    def run(self):
        """运行服务器"""
        try:
            self.running = True
            self.start_time = time.time()
            
            host = self.settings.get('server.host', '0.0.0.0')
            port = self.settings.get('server.port', 8080)
            debug = self.settings.get('server.debug', False)
            
            self.logger.info(f"API服务器启动: {host}:{port}")
            
            self.socketio.run(
                self.app,
                host=host,
                port=port,
                debug=debug,
                use_reloader=False
            )
            
        except Exception as e:
            self.logger.error(f"API服务器运行失败: {e}")
            self.running = False
    
    def stop(self):
        """停止服务器"""
        try:
            self.running = False
            self.logger.info("API服务器已停止")
        except Exception as e:
            self.logger.error(f"停止API服务器失败: {e}")
    
    def get_app(self):
        """获取Flask应用实例"""
        return self.app
    
    def get_socketio(self):
        """获取SocketIO实例"""
        return self.socketio
