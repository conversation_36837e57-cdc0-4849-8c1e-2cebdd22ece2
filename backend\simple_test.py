#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("导入模块...")
    from config.database import DatabaseManager
    from models.file_share import SharedFolder, SharedFile
    
    print("初始化数据库...")
    db = DatabaseManager()
    db.init_database()
    
    print("查询数据...")
    with db.get_session() as session:
        folders = session.query(SharedFolder).all()
        print(f'共享文件夹数量: {len(folders)}')
        
        for folder in folders:
            print(f'文件夹: {folder.name} -> {folder.path}')
            files = session.query(SharedFile).filter_by(folder_id=folder.id).all()
            print(f'  文件数量: {len(files)}')
            
            # 检查图片文件
            image_count = 0
            for file in files:
                filename = file.filename
                if filename:
                    ext = filename.lower().split('.')[-1] if '.' in filename else ''
                    allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                    if ext in allowed_exts:
                        image_count += 1
                        if image_count <= 3:  # 只显示前3个
                            print(f'    🖼️ {filename} ({file.file_size} bytes)')
            
            print(f'  图片文件数量: {image_count}')
            print()
    
    print("✅ 测试完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
