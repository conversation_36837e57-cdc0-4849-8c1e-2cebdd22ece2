#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime
from typing import Optional

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, server_instance):
        self.root = root
        self.server = server_instance
        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        self.start_status_update()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("企业级文件共享系统 - 服务端 v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_statusbar()
        
        # 创建主要内容区域
        self.create_main_content()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 服务器菜单
        server_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="服务器", menu=server_menu)
        server_menu.add_command(label="启动服务器", command=self.start_server)
        server_menu.add_command(label="停止服务器", command=self.stop_server)
        server_menu.add_command(label="重启服务器", command=self.restart_server)
        server_menu.add_separator()
        server_menu.add_command(label="服务器设置", command=self.open_server_settings)
        
        # 管理菜单
        manage_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="管理", menu=manage_menu)
        manage_menu.add_command(label="用户管理", command=self.open_user_management)
        manage_menu.add_command(label="文件管理", command=self.open_file_management)
        manage_menu.add_command(label="权限管理", command=self.open_permission_management)
        manage_menu.add_command(label="活动日志", command=self.open_activity_log)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据库备份", command=self.backup_database)
        tools_menu.add_command(label="数据库恢复", command=self.restore_database)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="系统诊断", command=self.system_diagnosis)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用手册", command=self.show_manual)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.main_frame)
        
        # 服务器控制按钮
        self.start_btn = ttk.Button(self.toolbar, text="启动服务器", 
                                   command=self.start_server)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(self.toolbar, text="停止服务器", 
                                  command=self.stop_server, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 管理按钮
        ttk.Button(self.toolbar, text="用户管理", 
                  command=self.open_user_management).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(self.toolbar, text="文件管理", 
                  command=self.open_file_management).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(self.toolbar, text="监控", 
                  command=self.open_monitoring).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 设置按钮
        ttk.Button(self.toolbar, text="设置", 
                  command=self.open_settings).pack(side=tk.LEFT, padx=2)
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = ttk.Frame(self.main_frame)
        
        # 服务器状态
        self.status_label = ttk.Label(self.statusbar, text="服务器已停止")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                              fill=tk.Y, padx=5)
        
        # 在线用户数
        self.users_label = ttk.Label(self.statusbar, text="在线用户: 0")
        self.users_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                              fill=tk.Y, padx=5)
        
        # 系统时间
        self.time_label = ttk.Label(self.statusbar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 概览标签页
        self.create_overview_tab()
        
        # 实时监控标签页
        self.create_monitoring_tab()
        
        # 系统日志标签页
        self.create_log_tab()
        
        # 通知标签页
        self.create_notification_tab()
    
    def create_overview_tab(self):
        """创建概览标签页"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="系统概览")
        
        # 左侧统计信息
        left_frame = ttk.LabelFrame(overview_frame, text="系统统计")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 统计信息标签
        self.stats_labels = {}
        stats_items = [
            ("总用户数", "total_users"),
            ("在线用户", "online_users"),
            ("共享文件夹", "shared_folders"),
            ("总文件数", "total_files"),
            ("总文件大小", "total_size"),
            ("今日下载", "today_downloads"),
            ("今日上传", "today_uploads"),
            ("今日搜索", "today_searches")
        ]
        
        for i, (label, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(left_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                        sticky=tk.W, padx=5, pady=2)
            self.stats_labels[key] = ttk.Label(left_frame, text="0")
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, 
                                       padx=5, pady=2)
        
        # 右侧快速操作
        right_frame = ttk.LabelFrame(overview_frame, text="快速操作")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        quick_actions = [
            ("添加共享文件夹", self.add_shared_folder),
            ("创建用户", self.create_user),
            ("查看活动日志", self.open_activity_log),
            ("系统设置", self.open_settings),
            ("数据库备份", self.backup_database)
        ]
        
        for text, command in quick_actions:
            ttk.Button(right_frame, text=text, command=command, 
                      width=20).pack(pady=2, padx=5)
    
    def create_monitoring_tab(self):
        """创建监控标签页"""
        monitoring_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitoring_frame, text="实时监控")
        
        # 在线用户列表
        users_frame = ttk.LabelFrame(monitoring_frame, text="在线用户")
        users_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建树形视图
        columns = ("用户名", "IP地址", "登录时间", "活动状态", "操作")
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings")
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120)
        
        # 添加滚动条
        users_scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL, 
                                       command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="系统日志")
        
        # 日志显示区域
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, 
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_notification_tab(self):
        """创建通知标签页"""
        notification_frame = ttk.Frame(self.notebook)
        self.notebook.add(notification_frame, text="系统通知")
        
        # 通知控制
        control_frame = ttk.Frame(notification_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(control_frame, text="通知内容:").pack(side=tk.LEFT)
        self.notification_entry = ttk.Entry(control_frame, width=50)
        self.notification_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="发送通知", 
                  command=self.send_notification).pack(side=tk.LEFT, padx=5)
        
        # 通知历史
        history_frame = ttk.LabelFrame(notification_frame, text="通知历史")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.notification_listbox = tk.Listbox(history_frame)
        notification_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, 
                                              command=self.notification_listbox.yview)
        self.notification_listbox.configure(yscrollcommand=notification_scrollbar.set)
        
        self.notification_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notification_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.toolbar.pack(fill=tk.X, pady=2)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.statusbar.pack(fill=tk.X, pady=2)
    
    def start_status_update(self):
        """启动状态更新线程"""
        def update_status():
            while True:
                try:
                    # 更新时间
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.time_label.config(text=current_time)
                    
                    # 更新统计信息
                    self.update_statistics()
                    
                    time.sleep(1)
                except:
                    break
        
        status_thread = threading.Thread(target=update_status, daemon=True)
        status_thread.start()
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            # 这里应该从服务器获取实际数据
            # 暂时使用模拟数据
            stats = {
                "total_users": "0",
                "online_users": "0",
                "shared_folders": "0",
                "total_files": "0",
                "total_size": "0 MB",
                "today_downloads": "0",
                "today_uploads": "0",
                "today_searches": "0"
            }
            
            for key, value in stats.items():
                if key in self.stats_labels:
                    self.stats_labels[key].config(text=value)
        except:
            pass
    
    def update_status(self, message: str, color: str = "black"):
        """更新状态栏"""
        self.status_label.config(text=message, foreground=color)
    
    # 菜单和按钮事件处理方法
    def start_server(self):
        """启动服务器"""
        self.server.start_server()
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
    
    def stop_server(self):
        """停止服务器"""
        self.server.stop_server()
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
    
    def restart_server(self):
        """重启服务器"""
        self.stop_server()
        time.sleep(1)
        self.start_server()
    
    # 占位方法（后续实现）
    def import_config(self): pass
    def export_config(self): pass
    def open_server_settings(self): pass
    def open_user_management(self): pass
    def open_file_management(self): pass
    def open_permission_management(self): pass
    def open_activity_log(self): pass
    def open_monitoring(self): pass
    def open_settings(self): pass
    def backup_database(self): pass
    def restore_database(self): pass
    def clear_cache(self): pass
    def system_diagnosis(self): pass
    def show_manual(self): pass
    def show_about(self): pass
    def add_shared_folder(self): pass
    def create_user(self): pass
    def send_notification(self): pass
