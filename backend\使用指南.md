# 企业级文件共享系统 - 使用指南

## 🚀 快速开始

### 系统要求
- Windows 7/10/11
- Python 3.7+
- MySQL 5.7+ (可选，完整版需要)
- 4GB+ 内存
- 10GB+ 硬盘空间

### 立即体验（简化版）

1. **打开命令行**
   ```bash
   cd C:\Users\<USER>\Desktop\Net\backend
   ```

2. **启动系统**
   ```bash
   python main_simple.py
   ```

3. **使用界面**
   - 系统会自动打开GUI管理界面
   - 点击"启动服务器"开始服务
   - 查看系统概览和日志信息

## 📋 功能说明

### 主界面功能

#### 1. 系统状态区域
- **服务器状态**: 显示当前服务器运行状态
- **状态指示**: 绿色=运行中，红色=已停止

#### 2. 控制按钮
- **启动服务器**: 启动文件共享服务
- **停止服务器**: 停止文件共享服务  
- **系统设置**: 配置系统参数（开发中）
- **用户管理**: 管理用户账户（开发中）

#### 3. 信息显示区域

**系统概览标签页**
- 系统版本和状态信息
- 功能特性列表
- 技术架构说明
- 部署要求和使用说明

**系统日志标签页**
- 实时系统日志显示
- 操作记录和错误信息
- 自动滚动到最新日志

#### 4. 状态栏
- 当前时间显示
- 系统就绪状态

### 核心功能特性

#### 🔐 权限管理
- **用户组管理**: admin、user、guest三级权限
- **文件夹权限**: 读取、写入、删除、上传、下载控制
- **网络访问**: 内网/外网访问控制
- **会话管理**: 安全的用户会话控制

#### 🔍 双搜索引擎
- **文本搜索**: 类似Everything的快速文件名搜索
- **图像搜索**: 基于OpenCV的图像识别搜索
- **通配符支持**: 支持 * 和 ? 通配符
- **实时索引**: 自动维护搜索索引

#### 🖼️ 缩略图支持
- **多格式支持**: JPG、PNG、PSD、TIF、AI、EPS等
- **多尺寸**: 小、中、大、超大四种尺寸
- **自动生成**: 文件扫描时自动生成缩略图
- **缓存管理**: 智能缓存管理

#### 📦 下载功能
- **单文件下载**: 直接下载单个文件
- **批量下载**: 多选文件批量下载
- **文件夹下载**: 整个文件夹打包下载
- **加密下载**: N次下载后自动加密保护

#### 📊 监控统计
- **实时监控**: 系统性能和用户活动
- **在线用户**: 当前在线用户列表
- **活动日志**: 详细的用户行为记录
- **统计报表**: 下载、上传、搜索统计

#### 📢 通知系统
- **滚动通知**: 系统消息滚动显示
- **截图支持**: 通知可包含截图
- **历史记录**: 通知历史查看
- **实时推送**: WebSocket实时通知

## 🛠️ 完整版部署

### 1. 环境准备

#### 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 安装MySQL
1. 下载MySQL 8.0
2. 安装并设置root密码为: `123456`
3. 启动MySQL服务

### 2. 数据库初始化
```bash
python init_database.py
```

### 3. 启动完整版系统
```bash
python main.py
```

### 4. 系统测试
```bash
python test_system.py
```

## 🔧 配置说明

### 系统配置文件
配置文件位置: `config/settings.py`

#### 服务器配置
```python
"server": {
    "host": "0.0.0.0",      # 服务器地址
    "port": 8080,           # 服务器端口
    "debug": False          # 调试模式
}
```

#### 数据库配置
```python
"database": {
    "host": "localhost",    # 数据库地址
    "port": 3306,          # 数据库端口
    "username": "root",     # 用户名
    "password": "123456",   # 密码
    "database": "file_share_system"  # 数据库名
}
```

#### 文件共享配置
```python
"file_share": {
    "max_file_size": **********,  # 最大文件大小(1GB)
    "allowed_extensions": [       # 允许的文件扩展名
        ".jpg", ".png", ".pdf", ...
    ]
}
```

### 默认账户
- **管理员账户**: admin / admin123
- **权限**: 完整系统管理权限

## 📡 API接口

### 基础接口
- `GET /api/health` - 健康检查
- `GET /api/server/status` - 服务器状态

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 文件接口
- `GET /api/files/folders` - 获取共享文件夹
- `GET /api/files/folders/{id}/files` - 获取文件列表
- `GET /api/files/{id}/download` - 下载文件

### 搜索接口
- `POST /api/search` - 文件搜索

### 管理接口
- `GET /api/admin/users` - 用户管理
- `GET /api/admin/stats` - 系统统计

## 🚨 故障排除

### 常见问题

#### 1. 系统无法启动
**问题**: Python版本过低
**解决**: 升级到Python 3.7+

**问题**: 缺少依赖包
**解决**: 运行 `pip install -r requirements.txt`

#### 2. 数据库连接失败
**问题**: MySQL服务未启动
**解决**: 启动MySQL服务

**问题**: 密码错误
**解决**: 检查MySQL root密码是否为 `123456`

#### 3. GUI界面无法显示
**问题**: tkinter未安装
**解决**: 重新安装Python，确保包含tkinter

#### 4. 端口占用
**问题**: 8080端口被占用
**解决**: 修改配置文件中的端口号

### 日志查看
- **系统日志**: `logs/SimpleFileShareServer.log`
- **错误日志**: `logs/SimpleFileShareServer_error.log`
- **GUI日志**: 主界面"系统日志"标签页

## 📞 技术支持

### 系统信息
- **版本**: 1.0.0
- **开发语言**: Python
- **GUI框架**: tkinter
- **数据库**: MySQL
- **支持平台**: Windows

### 联系方式
如有技术问题，请查看：
1. 系统日志文件
2. 错误提示信息
3. 项目文档

## 🎯 使用建议

### 最佳实践
1. **定期备份**: 定期备份数据库和配置文件
2. **监控日志**: 定期查看系统日志
3. **权限管理**: 合理设置用户权限
4. **性能监控**: 关注系统性能指标

### 安全建议
1. **修改默认密码**: 修改默认管理员密码
2. **网络安全**: 配置防火墙规则
3. **定期更新**: 保持系统和依赖包更新
4. **访问控制**: 限制外网访问

## 🔄 版本说明

### 当前版本 (v1.0.0)
- ✅ 基础GUI界面
- ✅ 系统监控功能
- ✅ 日志记录系统
- ✅ 服务器控制功能
- ✅ 完整架构设计

### 计划功能
- 🔄 完整数据库集成
- 🔄 文件上传下载
- 🔄 用户管理界面
- 🔄 搜索功能实现
- 🔄 缩略图生成

---

**🎉 恭喜！您已成功部署企业级文件共享系统！**

系统现在已经运行，您可以通过GUI界面管理和监控系统状态。如需完整功能，请按照完整版部署指南进行配置。
