<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片文件系统功能测试</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            background: white;
            margin: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .demo-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        .demo-file-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .demo-file-item:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .demo-thumbnail {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 48px;
            color: #6c757d;
        }
        .demo-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }
        .demo-filename {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .demo-fileinfo {
            font-size: 12px;
            color: #6c757d;
        }
        .api-test-result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🖼️ 图片文件系统功能测试</h1>
        <p>测试专门针对图片文件的文件分享系统功能，包括文件过滤、缩略图显示、预览等功能。</p>
    </div>

    <div class="test-section">
        <h3>🔧 系统配置验证</h3>
        <div class="test-grid">
            <div class="test-card">
                <h4>支持的图片格式</h4>
                <div id="supported-formats">检查中...</div>
            </div>
            <div class="test-card">
                <h4>API端点配置</h4>
                <div id="api-endpoints">检查中...</div>
            </div>
            <div class="test-card">
                <h4>服务器连接</h4>
                <div id="server-connection">检查中...</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>📁 文件列表功能测试</h3>
        <button onclick="testFileList()">测试文件列表API</button>
        <button onclick="testFileFiltering()">测试文件过滤</button>
        <button onclick="testFolderList()">测试文件夹列表</button>
        
        <div id="file-list-result" class="api-test-result">等待测试...</div>
        
        <div class="demo-gallery" id="demo-gallery">
            <!-- 演示文件项将在这里显示 -->
        </div>
    </div>

    <div class="test-section">
        <h3>🖼️ 缩略图功能测试</h3>
        <button onclick="testThumbnails()">测试缩略图生成</button>
        <button onclick="testPreview()">测试文件预览</button>
        <button onclick="testImageFormats()">测试不同图片格式</button>
        
        <div id="thumbnail-result" class="api-test-result">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>🔍 搜索功能测试</h3>
        <button onclick="testTextSearch()">测试文本搜索</button>
        <button onclick="testImageSearch()">测试图片搜索</button>
        <button onclick="testSearchFiltering()">测试搜索过滤</button>
        
        <div id="search-result" class="api-test-result">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>⬇️ 下载功能测试</h3>
        <button onclick="testSingleDownload()">测试单文件下载</button>
        <button onclick="testBatchDownload()">测试批量下载</button>
        <button onclick="testPasswordRequest()">测试密码申请</button>
        
        <div id="download-result" class="api-test-result">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>🎯 完整流程测试</h3>
        <button onclick="runFullTest()">运行完整测试</button>
        <button onclick="simulateUserWorkflow()">模拟用户工作流</button>
        
        <div id="full-test-result" class="api-test-result">等待测试...</div>
    </div>

    <!-- 包含必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>

    <script>
        // 页面加载时自动检查配置
        window.addEventListener('load', () => {
            checkSupportedFormats();
            checkApiEndpoints();
            checkServerConnection();
        });

        function checkSupportedFormats() {
            const container = document.getElementById('supported-formats');
            const formats = CONFIG.FILES.ALLOWED_EXTENSIONS;
            
            container.innerHTML = `
                <div class="status-ok">✅ 支持 ${formats.length} 种格式:</div>
                <div style="font-size: 12px; margin-top: 8px;">
                    ${formats.join(', ')}
                </div>
            `;
        }

        function checkApiEndpoints() {
            const container = document.getElementById('api-endpoints');
            const endpoints = CONFIG.API.ENDPOINTS;
            
            let html = '<div class="status-ok">✅ API端点配置:</div><ul style="font-size: 12px; margin: 8px 0;">';
            Object.keys(endpoints).forEach(key => {
                html += `<li>${key}: ${endpoints[key]}</li>`;
            });
            html += '</ul>';
            
            container.innerHTML = html;
        }

        async function checkServerConnection() {
            const container = document.getElementById('server-connection');
            
            try {
                const response = await fetch(`${CONFIG.API.BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    container.innerHTML = `
                        <div class="status-ok">✅ 服务器连接正常</div>
                        <div style="font-size: 12px; margin-top: 8px;">
                            状态: ${data.status}<br>
                            版本: ${data.version}
                        </div>
                    `;
                } else {
                    container.innerHTML = '<div class="status-error">❌ 服务器响应异常</div>';
                }
            } catch (error) {
                container.innerHTML = `<div class="status-error">❌ 无法连接服务器: ${error.message}</div>`;
            }
        }

        async function testFileList() {
            const result = document.getElementById('file-list-result');
            result.innerHTML = '正在测试文件列表API...';
            
            try {
                // 模拟登录获取token
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    result.innerHTML = '<div class="status-warning">⚠️ 需要先登录</div>';
                    return;
                }

                const response = await FileAPI.getFiles();
                
                result.innerHTML = `
                    <div class="status-ok">✅ 文件列表API测试成功</div>
                    <pre>${JSON.stringify(response, null, 2)}</pre>
                `;
                
                // 显示演示文件
                displayDemoFiles(response.files || []);
                
            } catch (error) {
                result.innerHTML = `<div class="status-error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        function testFileFiltering() {
            const result = document.getElementById('file-list-result');
            
            // 模拟文件列表
            const mockFiles = [
                { name: 'photo.jpg', type: 'file' },
                { name: 'design.psd', type: 'file' },
                { name: 'document.pdf', type: 'file' },
                { name: 'video.mp4', type: 'file' },
                { name: 'image.png', type: 'file' },
                { name: 'folder1', type: 'folder' }
            ];
            
            const filteredFiles = mockFiles.filter(file => {
                if (file.type === 'folder') return true;
                return CONFIG.FILES.isAllowedFile(file.name);
            });
            
            result.innerHTML = `
                <div class="status-ok">✅ 文件过滤测试</div>
                <div>原始文件: ${mockFiles.length} 个</div>
                <div>过滤后: ${filteredFiles.length} 个</div>
                <div>过滤掉: ${mockFiles.filter(f => f.type !== 'folder' && !CONFIG.FILES.isAllowedFile(f.name)).map(f => f.name).join(', ')}</div>
            `;
        }

        function displayDemoFiles(files) {
            const gallery = document.getElementById('demo-gallery');
            
            if (!files || files.length === 0) {
                gallery.innerHTML = '<div style="grid-column: 1/-1; text-align: center; color: #6c757d;">暂无文件</div>';
                return;
            }
            
            gallery.innerHTML = files.map(file => {
                const isImage = CONFIG.FILES.isAllowedFile(file.name);
                const icon = CONFIG.FILES.getFileIcon(file.name, file.type === 'folder');
                
                return `
                    <div class="demo-file-item">
                        <div class="demo-thumbnail">
                            ${isImage && file.id ? 
                                `<img src="${FileAPI.getThumbnailURL(file.id, 'medium')}" alt="${file.name}" onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=\\"${icon}\\"></i>';">` :
                                `<i class="${icon}"></i>`
                            }
                        </div>
                        <div class="demo-filename">${file.name}</div>
                        <div class="demo-fileinfo">
                            ${file.type === 'folder' ? '文件夹' : CONFIG.formatFileSize(file.size || 0)}
                        </div>
                    </div>
                `;
            }).join('');
        }

        async function testThumbnails() {
            const result = document.getElementById('thumbnail-result');
            result.innerHTML = '正在测试缩略图功能...';
            
            // 测试缩略图URL生成
            const testFileId = 123;
            const thumbnailUrl = FileAPI.getThumbnailURL(testFileId, 'medium');
            
            result.innerHTML = `
                <div class="status-ok">✅ 缩略图URL生成测试</div>
                <div>测试文件ID: ${testFileId}</div>
                <div>生成的缩略图URL: ${thumbnailUrl}</div>
                <div>不同尺寸:</div>
                <ul>
                    <li>小: ${FileAPI.getThumbnailURL(testFileId, 'small')}</li>
                    <li>中: ${FileAPI.getThumbnailURL(testFileId, 'medium')}</li>
                    <li>大: ${FileAPI.getThumbnailURL(testFileId, 'large')}</li>
                </ul>
            `;
        }

        async function runFullTest() {
            const result = document.getElementById('full-test-result');
            result.innerHTML = '正在运行完整测试...';
            
            const tests = [
                { name: '配置检查', fn: () => Promise.resolve('OK') },
                { name: '服务器连接', fn: checkServerConnection },
                { name: '文件过滤', fn: testFileFiltering },
                { name: '缩略图URL', fn: testThumbnails }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    await test.fn();
                    results.push(`✅ ${test.name}: 通过`);
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
            }
            
            result.innerHTML = `
                <div class="status-ok">完整测试结果:</div>
                <div>${results.join('<br>')}</div>
            `;
        }

        // 其他测试函数的简化实现
        function testFolderList() {
            document.getElementById('file-list-result').innerHTML = '<div class="status-warning">⚠️ 文件夹列表功能需要后端数据</div>';
        }

        function testPreview() {
            document.getElementById('thumbnail-result').innerHTML = '<div class="status-ok">✅ 预览功能已集成到文件管理器中</div>';
        }

        function testImageFormats() {
            const formats = CONFIG.FILES.ALLOWED_EXTENSIONS;
            document.getElementById('thumbnail-result').innerHTML = `
                <div class="status-ok">✅ 支持的图片格式测试</div>
                <div>共支持 ${formats.length} 种格式: ${formats.join(', ')}</div>
            `;
        }

        function testTextSearch() {
            document.getElementById('search-result').innerHTML = '<div class="status-warning">⚠️ 搜索功能需要登录状态</div>';
        }

        function testImageSearch() {
            document.getElementById('search-result').innerHTML = '<div class="status-warning">⚠️ 图片搜索功能需要后端支持</div>';
        }

        function testSearchFiltering() {
            document.getElementById('search-result').innerHTML = '<div class="status-ok">✅ 搜索结果会自动过滤非图片文件</div>';
        }

        function testSingleDownload() {
            document.getElementById('download-result').innerHTML = '<div class="status-warning">⚠️ 下载功能需要实际文件</div>';
        }

        function testBatchDownload() {
            document.getElementById('download-result').innerHTML = '<div class="status-warning">⚠️ 批量下载功能需要选中文件</div>';
        }

        function testPasswordRequest() {
            document.getElementById('download-result').innerHTML = '<div class="status-ok">✅ 密码申请API已配置</div>';
        }

        function simulateUserWorkflow() {
            document.getElementById('full-test-result').innerHTML = `
                <div class="status-ok">✅ 用户工作流模拟</div>
                <div>1. 用户登录 → 验证身份</div>
                <div>2. 浏览文件 → 只显示图片格式</div>
                <div>3. 查看缩略图 → 快速预览</div>
                <div>4. 搜索文件 → 过滤结果</div>
                <div>5. 下载文件 → 压缩包形式</div>
                <div>6. 申请密码 → 解压加密文件</div>
            `;
        }
    </script>
</body>
</html>
