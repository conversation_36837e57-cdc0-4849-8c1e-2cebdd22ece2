#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器启动脚本
用于快速启动和测试文件共享系统
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def install_dependencies():
    """安装依赖包"""
    print("正在检查和安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        requirements_file = Path(__file__).parent / "requirements.txt"
        if not requirements_file.exists():
            print("错误: requirements.txt 文件不存在")
            return False
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"安装依赖失败: {result.stderr}")
            return False
        
        print("依赖包安装完成")
        return True
        
    except Exception as e:
        print(f"安装依赖时出错: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("正在创建必要的目录...")
    
    directories = [
        "data",
        "data/search_index",
        "data/thumbnails", 
        "logs",
        "temp",
        "backup"
    ]
    
    base_path = Path(__file__).parent
    
    for directory in directories:
        dir_path = base_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")
    
    print("目录创建完成")

def check_mysql_connection():
    """检查MySQL连接"""
    print("正在检查MySQL连接...")
    
    try:
        import pymysql
        
        # 使用默认配置测试连接
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL连接成功, 版本: {version[0]}")
        
        connection.close()
        return True
        
    except ImportError:
        print("错误: PyMySQL未安装")
        return False
    except Exception as e:
        print(f"MySQL连接失败: {e}")
        print("请确保MySQL服务正在运行，并且用户名密码正确")
        print("默认配置: host=localhost, port=3306, user=root, password=123456")
        return False

def start_server():
    """启动服务器"""
    print("正在启动文件共享系统服务端...")
    
    try:
        # 添加当前目录到Python路径
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # 导入并运行主程序
        from main import main
        main()
        
    except KeyboardInterrupt:
        print("\n服务器被用户中断")
    except Exception as e:
        print(f"启动服务器失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("企业级文件共享系统 - 服务端启动器")
    print("版本: 1.0.0")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 询问是否安装依赖
    install_deps = input("是否安装/更新依赖包? (y/n, 默认n): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("依赖安装失败，但可以尝试继续运行")
    
    # 检查MySQL连接
    if not check_mysql_connection():
        continue_anyway = input("MySQL连接失败，是否继续启动? (y/n, 默认n): ").lower().strip()
        if continue_anyway not in ['y', 'yes']:
            print("请配置MySQL后重新启动")
            sys.exit(1)
    
    print("\n准备启动服务器...")
    time.sleep(2)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
