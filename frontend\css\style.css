/* 全局样式 */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    
    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    /* 边框 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* 过渡 */
    --transition: all 0.2s ease-in-out;
    --transition-fast: all 0.15s ease-in-out;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--gray-800);
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 通用类 */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-200);
}

.btn-ghost {
    background-color: transparent;
    color: var(--gray-600);
}

.btn-ghost:hover:not(:disabled) {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
}

/* 输入框样式 */
.input {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.input::placeholder {
    color: var(--gray-400);
}

/* 卡片样式 */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease-in-out;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 导航栏 */
.navbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
}

.nav-brand i {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.nav-search {
    flex: 1;
    max-width: 600px;
    margin: 0 var(--spacing-8);
}

.search-container {
    position: relative;
}

.search-container i {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-container input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) var(--spacing-10);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background-color: var(--bg-secondary);
    transition: var(--transition);
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-md);
}

.search-filters {
    display: flex;
    gap: var(--spacing-2);
    margin-top: var(--spacing-2);
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background-color: var(--gray-100);
}

.filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
}

.nav-btn:hover {
    background-color: var(--gray-100);
}

.nav-btn i {
    font-size: var(--font-size-sm);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.user-avatar:hover {
    background-color: var(--primary-hover);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    padding: var(--spacing-4);
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
}

.user-role {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--gray-100);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 64px);
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-6);
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: var(--spacing-8);
}

.sidebar-section h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-4);
}

.sidebar-menu {
    list-style: none;
}

.menu-item {
    margin-bottom: var(--spacing-1);
}

.menu-item a {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.menu-item a:hover {
    background-color: var(--gray-100);
}

.menu-item.active a {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

.folder-list {
    list-style: none;
}

.folder-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.folder-item:hover {
    background-color: var(--gray-100);
}

.folder-item i {
    color: var(--warning-color);
}

.storage-info {
    padding: var(--spacing-4);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.storage-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transition: width 0.3s ease;
}

.storage-text {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

/* 系统状态样式 */
.system-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.status-item:hover {
    background: var(--bg-hover);
}

.status-item i {
    width: 20px;
    text-align: center;
    color: var(--primary-color);
    font-size: 14px;
}

.status-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.status-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.status-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.status-value.online {
    color: var(--success-color);
}

.status-value.offline {
    color: var(--error-color);
}

/* 用户菜单样式 */
.user-menu {
    position: relative;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown .user-info {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.user-dropdown .user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-dropdown .user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

.user-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown .dropdown-item:hover {
    background: var(--bg-hover);
}

.user-dropdown .dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

/* 通知面板样式 */
.notification-panel {
    position: fixed;
    top: 60px;
    right: 20px;
    width: 350px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.notification-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-panel {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
}

.close-panel:hover {
    color: var(--text-primary);
}
