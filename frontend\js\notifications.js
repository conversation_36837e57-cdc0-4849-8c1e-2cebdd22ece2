/**
 * 通知管理模块
 * 处理系统通知、实时通知、通知历史等功能
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.init();
    }
    
    /**
     * 初始化通知管理器
     */
    init() {
        this.bindEvents();
        this.loadNotifications();
        this.connectWebSocket();
        this.startPeriodicCheck();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 通知按钮
        const notificationBtn = Utils.dom.$('#notifications-btn');
        if (notificationBtn) {
            Utils.event.on(notificationBtn, 'click', () => {
                this.toggleNotificationPanel();
            });
        }
        
        // 关闭通知面板
        const closePanel = Utils.dom.$('.close-panel');
        if (closePanel) {
            Utils.event.on(closePanel, 'click', () => {
                this.hideNotificationPanel();
            });
        }
        
        // 点击外部关闭面板
        Utils.event.on(document, 'click', (e) => {
            const panel = Utils.dom.$('#notification-panel');
            const btn = Utils.dom.$('#notifications-btn');
            
            if (panel && Utils.dom.hasClass(panel, 'show') && 
                !panel.contains(e.target) && !btn.contains(e.target)) {
                this.hideNotificationPanel();
            }
        });
    }
    
    /**
     * 加载通知
     */
    async loadNotifications() {
        try {
            const response = await SystemAPI.getNotifications();
            this.notifications = response.notifications || [];
            this.updateNotificationDisplay();
        } catch (error) {
            CONFIG.log('error', 'Failed to load notifications:', error);
        }
    }
    
    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        try {
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/ws/notifications`;
            
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                CONFIG.log('info', 'WebSocket connected');
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const notification = JSON.parse(event.data);
                    this.handleRealtimeNotification(notification);
                } catch (error) {
                    CONFIG.log('error', 'Failed to parse notification:', error);
                }
            };
            
            this.ws.onclose = () => {
                this.isConnected = false;
                CONFIG.log('warn', 'WebSocket disconnected');
                this.attemptReconnect();
            };
            
            this.ws.onerror = (error) => {
                CONFIG.log('error', 'WebSocket error:', error);
            };
            
        } catch (error) {
            CONFIG.log('error', 'Failed to connect WebSocket:', error);
        }
    }
    
    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            CONFIG.log('info', `Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
            
            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            CONFIG.log('error', 'Max reconnection attempts reached');
        }
    }
    
    /**
     * 处理实时通知
     */
    handleRealtimeNotification(notification) {
        // 添加到通知列表
        this.addNotification(notification);
        
        // 显示Toast通知
        this.showToastNotification(notification);
        
        // 播放通知声音
        this.playNotificationSound();
        
        // 更新显示
        this.updateNotificationDisplay();
    }
    
    /**
     * 添加通知
     */
    addNotification(notification) {
        const notificationItem = {
            id: notification.id || Utils.generateId('notification'),
            title: notification.title || '系统通知',
            message: notification.message || '',
            type: notification.type || 'info',
            timestamp: notification.timestamp || new Date().toISOString(),
            read: false,
            ...notification
        };
        
        this.notifications.unshift(notificationItem);
        
        // 限制通知数量
        if (this.notifications.length > CONFIG.UI.NOTIFICATION.MAX_COUNT * 2) {
            this.notifications = this.notifications.slice(0, CONFIG.UI.NOTIFICATION.MAX_COUNT * 2);
        }
        
        this.unreadCount++;
    }
    
    /**
     * 显示Toast通知
     */
    showToastNotification(notification) {
        const type = notification.type || 'info';
        const message = notification.message || notification.title || '新通知';
        
        Components.Toast.show(message, type, CONFIG.UI.NOTIFICATION.DURATION);
    }
    
    /**
     * 播放通知声音
     */
    playNotificationSound() {
        try {
            // 创建音频上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建简单的提示音
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
            
        } catch (error) {
            // 静默失败，不影响功能
            CONFIG.log('warn', 'Failed to play notification sound:', error);
        }
    }
    
    /**
     * 更新通知显示
     */
    updateNotificationDisplay() {
        this.updateNotificationBadge();
        this.renderNotificationList();
    }
    
    /**
     * 更新通知徽章
     */
    updateNotificationBadge() {
        const badge = Utils.dom.$('.notification-badge');
        if (badge) {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                Utils.dom.show(badge);
            } else {
                Utils.dom.hide(badge);
            }
        }
    }
    
    /**
     * 渲染通知列表
     */
    renderNotificationList() {
        const container = Utils.dom.$('#notification-list');
        if (!container) return;
        
        if (this.notifications.length === 0) {
            container.innerHTML = `
                <div class="empty-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>暂无通知</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.notifications.map(notification => 
            this.createNotificationHTML(notification)
        ).join('');
        
        // 绑定通知点击事件
        Utils.event.on(container, 'click', (e) => {
            const notificationItem = e.target.closest('.notification-item');
            if (notificationItem) {
                const notificationId = notificationItem.dataset.notificationId;
                this.markAsRead(notificationId);
            }
        });
    }
    
    /**
     * 创建通知HTML
     */
    createNotificationHTML(notification) {
        const timeAgo = Utils.formatDate(notification.timestamp);
        const readClass = notification.read ? 'read' : 'unread';
        
        return `
            <div class="notification-item ${notification.type} ${readClass}" 
                 data-notification-id="${notification.id}">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
                ${!notification.read ? '<div class="notification-dot"></div>' : ''}
            </div>
        `;
    }
    
    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const iconMap = {
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            upload: 'fas fa-upload',
            download: 'fas fa-download',
            share: 'fas fa-share',
            system: 'fas fa-cog'
        };
        return iconMap[type] || 'fas fa-bell';
    }
    
    /**
     * 标记为已读
     */
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            this.unreadCount = Math.max(0, this.unreadCount - 1);
            this.updateNotificationDisplay();
        }
    }
    
    /**
     * 标记全部为已读
     */
    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.unreadCount = 0;
        this.updateNotificationDisplay();
    }
    
    /**
     * 清除通知
     */
    clearNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.updateNotificationDisplay();
    }
    
    /**
     * 清除所有通知
     */
    clearAllNotifications() {
        this.notifications = [];
        this.unreadCount = 0;
        this.updateNotificationDisplay();
    }
    
    /**
     * 切换通知面板
     */
    toggleNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            if (Utils.dom.hasClass(panel, 'show')) {
                this.hideNotificationPanel();
            } else {
                this.showNotificationPanel();
            }
        }
    }
    
    /**
     * 显示通知面板
     */
    showNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            Utils.dom.addClass(panel, 'show');
            
            // 标记所有通知为已读
            setTimeout(() => {
                this.markAllAsRead();
            }, 1000);
        }
    }
    
    /**
     * 隐藏通知面板
     */
    hideNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            Utils.dom.removeClass(panel, 'show');
        }
    }
    
    /**
     * 开始定期检查
     */
    startPeriodicCheck() {
        // 每30秒检查一次新通知
        setInterval(() => {
            if (!this.isConnected) {
                this.loadNotifications();
            }
        }, 30000);
    }
    
    /**
     * 发送通知
     */
    async sendNotification(title, message, type = 'info') {
        try {
            // 如果有WebSocket连接，通过WebSocket发送
            if (this.isConnected && this.ws) {
                this.ws.send(JSON.stringify({
                    action: 'send_notification',
                    title,
                    message,
                    type
                }));
            } else {
                // 否则通过API发送
                await SystemAPI.sendNotification({ title, message, type });
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to send notification:', error);
        }
    }
    
    /**
     * 请求通知权限
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }
    
    /**
     * 显示浏览器通知
     */
    showBrowserNotification(title, message, icon = null) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: icon || '/assets/images/logo.png',
                badge: '/assets/images/badge.png'
            });
            
            // 点击通知时聚焦窗口
            notification.onclick = () => {
                window.focus();
                notification.close();
            };
            
            // 自动关闭
            setTimeout(() => {
                notification.close();
            }, 5000);
        }
    }
}

// 创建全局通知管理器实例
let notificationManager;

document.addEventListener('DOMContentLoaded', () => {
    notificationManager = new NotificationManager();
});

// 全局可用
window.NotificationManager = NotificationManager;
window.notificationManager = null;
