<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 后端连接测试</h1>
        
        <h3>测试不同端口的API连接</h3>
        <button onclick="testPort(8086)">测试 8086 端口</button>
        <button onclick="testPort(8085)">测试 8085 端口</button>
        <button onclick="testPort(8084)">测试 8084 端口</button>
        <button onclick="testPort(8082)">测试 8082 端口</button>
        <button onclick="testPort(5000)">测试 5000 端口</button>
        
        <div id="result" class="result">等待测试...</div>
    </div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.textContent += `[${timestamp}] ${message}\n`;
            result.scrollTop = result.scrollHeight;
        }

        async function testPort(port) {
            log(`测试端口 ${port}...`);
            
            const endpoints = [
                `/api/health`,
                `/api/server/status`,
                `/health`,
                `/status`,
                `/`
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const url = `http://localhost:${port}${endpoint}`;
                    log(`  尝试: ${url}`);
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        timeout: 5000
                    });
                    
                    if (response.ok) {
                        const contentType = response.headers.get('content-type');
                        let data;
                        
                        if (contentType && contentType.includes('application/json')) {
                            data = await response.json();
                            log(`  ✅ 成功 (${response.status}): ${JSON.stringify(data)}`);
                        } else {
                            const text = await response.text();
                            log(`  ✅ 成功 (${response.status}): ${text.substring(0, 100)}...`);
                        }
                        
                        // 如果找到了工作的端口，测试更多API
                        if (endpoint === '/api/health') {
                            await testMoreAPIs(port);
                        }
                        
                        return; // 找到工作的端点就停止
                    } else {
                        log(`  ❌ HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    log(`  ❌ 错误: ${error.message}`);
                }
            }
            
            log(`端口 ${port} 上没有找到可用的API`);
        }

        async function testMoreAPIs(port) {
            log(`\n测试端口 ${port} 上的更多API...`);
            
            const apiEndpoints = [
                '/api/files/folders',
                '/api/files',
                '/api/system/info'
            ];
            
            for (const endpoint of apiEndpoints) {
                try {
                    const url = `http://localhost:${port}${endpoint}`;
                    log(`  测试: ${url}`);
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        log(`  ✅ ${endpoint}: ${JSON.stringify(data).substring(0, 200)}...`);
                    } else if (response.status === 401) {
                        log(`  🔐 ${endpoint}: 需要认证 (${response.status})`);
                    } else {
                        log(`  ❌ ${endpoint}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    log(`  ❌ ${endpoint}: ${error.message}`);
                }
            }
        }

        // 页面加载时自动测试常见端口
        window.addEventListener('load', () => {
            log('开始自动测试常见端口...\n');
            
            setTimeout(() => testPort(8086), 500);
            setTimeout(() => testPort(8085), 1500);
            setTimeout(() => testPort(8084), 2500);
            setTimeout(() => testPort(5000), 3500);
        });
    </script>
</body>
</html>
