/**
 * 文件管理器核心模块
 * 处理文件列表、文件操作等核心功能
 */

class FileManager {
    constructor() {
        this.currentFolder = null;
        this.files = [];
        this.selectedFiles = new Set();
        this.viewMode = Utils.storage.get(CONFIG.STORAGE_KEYS.VIEW_MODE, 'grid');
        this.sortBy = Utils.storage.get(CONFIG.STORAGE_KEYS.SORT_ORDER, { field: 'name', order: 'asc' });
        
        this.init();
    }
    
    /**
     * 初始化文件管理器
     */
    init() {
        this.bindEvents();
        this.loadSharedFolders();
        this.loadFiles();
        this.setupViewMode();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 视图切换
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                const viewMode = e.target.dataset.view;
                this.setViewMode(viewMode);
            });
        });
        
        // 排序
        const sortSelect = Utils.dom.$('#sort-select');
        if (sortSelect) {
            Utils.event.on(sortSelect, 'change', (e) => {
                const [field, order] = e.target.value.split('-');
                this.setSortOrder(field, order || 'asc');
            });
        }
        
        // 文件夹导航
        Utils.event.on(document, 'click', (e) => {
            const folderItem = e.target.closest('.folder-item');
            if (folderItem) {
                const folderId = folderItem.dataset.folderId;
                this.navigateToFolder(folderId);
            }
        });
        
        // 面包屑导航
        Utils.event.on(document, 'click', (e) => {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem) {
                const folderId = breadcrumbItem.dataset.folderId;
                this.navigateToFolder(folderId);
            }
        });
        
        // 文件选择
        Utils.event.on(document, 'click', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileClick(fileItem, e);
            }
        });
        
        // 右键菜单
        Utils.event.on(document, 'contextmenu', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                e.preventDefault();
                this.showContextMenu(e.clientX, e.clientY, fileItem);
            }
        });
        
        // 双击文件
        Utils.event.on(document, 'dblclick', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileDoubleClick(fileItem);
            }
        });
        
        // 键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    
    /**
     * 加载共享文件夹
     */
    async loadSharedFolders() {
        try {
            const folders = await FolderAPI.getSharedFolders();
            this.renderSharedFolders(folders);
        } catch (error) {
            CONFIG.log('error', 'Failed to load shared folders:', error);
            Components.Toast.error('加载共享文件夹失败');
        }
    }
    
    /**
     * 渲染共享文件夹
     */
    renderSharedFolders(folders) {
        const container = Utils.dom.$('#shared-folders');
        if (!container) return;
        
        container.innerHTML = '';
        
        folders.forEach(folder => {
            const folderElement = Utils.dom.create('li', {
                className: 'folder-item',
                'data-folder-id': folder.id,
                innerHTML: `
                    <i class="fas fa-folder"></i>
                    <span>${folder.name}</span>
                `
            });
            
            container.appendChild(folderElement);
        });
    }
    
    /**
     * 加载文件列表
     */
    async loadFiles(folderId = null) {
        try {
            Components.Loading.show('正在加载文件...');

            CONFIG.log('info', `Loading files for folder: ${folderId}`);

            // 检查认证状态
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                Components.Toast.error('请先登录');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return;
            }

            const response = await FileAPI.getFiles(folderId);
            CONFIG.log('info', 'API Response:', response);

            // 处理不同的响应格式
            let files = [];
            if (response && response.files) {
                files = response.files;
            } else if (Array.isArray(response)) {
                files = response;
            } else if (response && response.data && Array.isArray(response.data)) {
                files = response.data;
            }

            CONFIG.log('info', `Received ${files.length} files before filtering`);

            // 过滤文件 - 只显示允许的图片格式
            files = this.filterAllowedFiles(files);
            CONFIG.log('info', `${files.length} files after filtering`);

            this.files = files;
            this.currentFolder = response.folder || null;

            this.renderFiles();
            this.updateBreadcrumb();

            // 显示加载结果
            if (files.length === 0) {
                // 如果没有文件，显示演示数据
                this.loadDemoData();
                Components.Toast.info('当前文件夹没有图片文件，显示演示数据');
            } else {
                Components.Toast.success(`加载了 ${files.length} 个文件`);
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load files:', error);

            // 更详细的错误信息
            let errorMessage = '加载文件失败';
            if (error.userMessage) {
                errorMessage = error.userMessage;
            } else if (error.message) {
                if (error.message.includes('401')) {
                    errorMessage = '请先登录';
                } else if (error.message.includes('403')) {
                    errorMessage = '没有访问权限';
                } else if (error.message.includes('404')) {
                    errorMessage = '文件夹不存在';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '无法连接到服务器';
                }
            }

            Components.Toast.error(errorMessage);

            // 如果是认证错误，跳转到登录页
            if (error.message && (error.message.includes('401') || error.message.includes('请先登录'))) {
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else {
                // 其他错误，显示演示数据
                this.loadDemoData();
                Components.Toast.info('加载失败，显示演示数据');
            }
        } finally {
            Components.Loading.hide();
        }
    }

    /**
     * 加载演示数据
     */
    loadDemoData() {
        const demoFiles = [
            {
                id: 1,
                name: 'sample-photo-1.jpg',
                type: 'file',
                size: 2048576,
                modified_at: '2024-01-15T10:30:00Z',
                folder_name: '演示文件夹'
            },
            {
                id: 2,
                name: 'design-mockup.psd',
                type: 'file',
                size: 15728640,
                modified_at: '2024-01-14T15:45:00Z',
                folder_name: '演示文件夹'
            },
            {
                id: 3,
                name: 'logo-vector.ai',
                type: 'file',
                size: 1024000,
                modified_at: '2024-01-13T09:20:00Z',
                folder_name: '演示文件夹'
            },
            {
                id: 4,
                name: 'screenshot.png',
                type: 'file',
                size: 512000,
                modified_at: '2024-01-12T14:10:00Z',
                folder_name: '演示文件夹'
            },
            {
                id: 5,
                name: '图片文件夹',
                type: 'folder',
                size: 0,
                modified_at: '2024-01-10T08:00:00Z',
                file_count: 25
            }
        ];

        this.files = demoFiles;
        this.renderFiles();
        this.updateBreadcrumb();
    }

    /**
     * 生成缩略图HTML
     */
    generateThumbnailHTML(file, icon) {
        // 检查是否为演示数据
        if (file.id <= 10) {
            // 演示数据，使用占位图片
            const demoImages = {
                1: 'https://picsum.photos/300/200?random=1',
                2: 'https://picsum.photos/300/200?random=2',
                3: 'https://picsum.photos/300/200?random=3',
                4: 'https://picsum.photos/300/200?random=4'
            };
            const demoUrl = demoImages[file.id] || 'https://picsum.photos/300/200?random=' + file.id;

            return `<img src="${demoUrl}" alt="${file.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <i class="${icon}" style="display:none;"></i>`;
        } else {
            // 真实数据，使用API缩略图
            const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');
            return `<img src="${thumbnailUrl}" alt="${file.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <i class="${icon}" style="display:none;"></i>`;
        }
    }

    /**
     * 过滤允许的文件 - 只显示图片格式
     */
    filterAllowedFiles(files) {
        return files.filter(file => {
            // 文件夹总是显示
            if (file.type === 'folder') {
                return true;
            }

            // 只显示允许的图片格式
            return CONFIG.FILES.isAllowedFile(file.name);
        });
    }
    
    /**
     * 渲染文件列表
     */
    renderFiles() {
        const sortedFiles = this.sortFiles(this.files);
        
        if (this.viewMode === 'grid') {
            this.renderGridView(sortedFiles);
        } else {
            this.renderListView(sortedFiles);
        }
    }
    
    /**
     * 渲染网格视图
     */
    renderGridView(files) {
        const container = Utils.dom.$('#file-grid');
        if (!container) return;
        
        container.innerHTML = '';
        Utils.dom.show(container);
        Utils.dom.hide(Utils.dom.$('#file-list'));
        
        files.forEach(file => {
            const fileElement = this.createFileGridItem(file);
            container.appendChild(fileElement);
        });
    }
    
    /**
     * 渲染列表视图
     */
    renderListView(files) {
        const container = Utils.dom.$('#file-list');
        const tbody = Utils.dom.$('#file-table-body');
        if (!container || !tbody) return;
        
        tbody.innerHTML = '';
        Utils.dom.show(container);
        Utils.dom.hide(Utils.dom.$('#file-grid'));
        
        files.forEach(file => {
            const fileElement = this.createFileListItem(file);
            tbody.appendChild(fileElement);
        });
    }
    
    /**
     * 创建网格文件项
     */
    createFileGridItem(file) {
        const isImage = Utils.isImageFile(file.name);
        const icon = CONFIG.FILES.getFileIcon(file.name, file.type === 'folder');
        
        return Utils.dom.create('div', {
            className: 'file-item',
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                <div class="file-icon">
                    ${isImage ?
                        this.generateThumbnailHTML(file, icon) :
                        `<i class="${icon}"></i>`
                    }
                </div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-meta">
                    <span class="file-size">${file.type === 'folder' ? '' : Utils.formatFileSize(file.size)}</span>
                    <span class="file-date">${Utils.formatDate(file.modified_at)}</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="fas fa-star"></i>
                    </button>
                </div>
            `
        });
    }
    
    /**
     * 创建列表文件项
     */
    createFileListItem(file) {
        const icon = CONFIG.FILES.getFileIcon(file.name, file.type === 'folder');
        
        return Utils.dom.create('tr', {
            className: 'file-item',
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                <td class="file-name-cell">
                    <div class="file-icon">
                        <i class="${icon}"></i>
                    </div>
                    <span title="${file.name}">${file.name}</span>
                </td>
                <td>${file.type === 'folder' ? '-' : Utils.formatFileSize(file.size)}</td>
                <td>${file.type === 'folder' ? '文件夹' : this.getFileTypeText(file.name)}</td>
                <td>${Utils.formatDate(file.modified_at)}</td>
                <td>
                    <div class="file-actions">
                        <button class="action-btn" data-action="download" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="action-btn" data-action="preview" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn" data-action="favorite" title="收藏">
                            <i class="fas fa-star"></i>
                        </button>
                    </div>
                </td>
            `
        });
    }
    
    /**
     * 获取文件类型文本 - 专门针对图片文件
     */
    getFileTypeText(filename) {
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));

        // 根据扩展名返回具体的图片类型
        const imageTypeMap = {
            '.jpg': 'JPEG图片',
            '.jpeg': 'JPEG图片',
            '.png': 'PNG图片',
            '.psd': 'Photoshop文档',
            '.tif': 'TIFF图片',
            '.tiff': 'TIFF图片',
            '.ai': 'Illustrator文档',
            '.eps': 'EPS矢量图',
            '.gif': 'GIF动图',
            '.bmp': 'BMP图片',
            '.webp': 'WebP图片',
            '.svg': 'SVG矢量图'
        };

        return imageTypeMap[ext] || '图片文件';
    }
    
    /**
     * 排序文件
     */
    sortFiles(files) {
        return [...files].sort((a, b) => {
            // 文件夹优先
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            let aValue, bValue;
            
            switch (this.sortBy.field) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'size':
                    aValue = a.size || 0;
                    bValue = b.size || 0;
                    break;
                case 'date':
                    aValue = new Date(a.modified_at);
                    bValue = new Date(b.modified_at);
                    break;
                case 'type':
                    aValue = this.getFileTypeText(a.name);
                    bValue = this.getFileTypeText(b.name);
                    break;
                default:
                    return 0;
            }
            
            if (aValue < bValue) return this.sortBy.order === 'asc' ? -1 : 1;
            if (aValue > bValue) return this.sortBy.order === 'asc' ? 1 : -1;
            return 0;
        });
    }
    
    /**
     * 设置视图模式
     */
    setViewMode(mode) {
        this.viewMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.VIEW_MODE, mode);
        
        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });
        
        this.renderFiles();
    }
    
    /**
     * 设置排序方式
     */
    setSortOrder(field, order = 'asc') {
        this.sortBy = { field, order };
        Utils.storage.set(CONFIG.STORAGE_KEYS.SORT_ORDER, this.sortBy);
        this.renderFiles();
    }
    
    /**
     * 设置视图模式
     */
    setupViewMode() {
        this.setViewMode(this.viewMode);
    }
    
    /**
     * 导航到文件夹
     */
    navigateToFolder(folderId) {
        this.selectedFiles.clear();
        this.loadFiles(folderId);
    }
    
    /**
     * 更新面包屑导航
     */
    updateBreadcrumb() {
        const container = Utils.dom.$('.breadcrumb-nav');
        if (!container) return;
        
        container.innerHTML = `
            <a href="#" class="breadcrumb-item" data-folder-id="">
                <i class="fas fa-home"></i>
                首页
            </a>
        `;
        
        if (this.currentFolder) {
            const folderLink = Utils.dom.create('a', {
                className: 'breadcrumb-item',
                'data-folder-id': this.currentFolder.id,
                textContent: this.currentFolder.name
            });
            container.appendChild(folderLink);
        }
    }
    
    /**
     * 处理文件点击
     */
    handleFileClick(fileItem, event) {
        const fileId = fileItem.dataset.fileId;
        
        if (event.ctrlKey || event.metaKey) {
            // 多选
            this.toggleFileSelection(fileId, fileItem);
        } else {
            // 单选
            this.clearSelection();
            this.selectFile(fileId, fileItem);
        }
    }
    
    /**
     * 处理文件双击
     */
    handleFileDoubleClick(fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        if (fileType === 'folder') {
            this.navigateToFolder(fileId);
        } else {
            this.previewFile(fileId);
        }
    }
    
    /**
     * 选择文件
     */
    selectFile(fileId, fileItem) {
        this.selectedFiles.add(fileId);
        Utils.dom.addClass(fileItem, 'selected');
    }
    
    /**
     * 切换文件选择状态
     */
    toggleFileSelection(fileId, fileItem) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            Utils.dom.removeClass(fileItem, 'selected');
        } else {
            this.selectedFiles.add(fileId);
            Utils.dom.addClass(fileItem, 'selected');
        }
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedFiles.clear();
        Utils.dom.$$('.file-item.selected').forEach(item => {
            Utils.dom.removeClass(item, 'selected');
        });
    }
    
    /**
     * 显示右键菜单
     */
    showContextMenu(x, y, fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        const menuItems = [
            {
                icon: 'fas fa-download',
                text: '下载',
                action: 'download',
                handler: () => this.downloadFile(fileId)
            },
            {
                icon: 'fas fa-eye',
                text: '预览',
                action: 'preview',
                handler: () => this.previewFile(fileId)
            },
            {
                icon: 'fas fa-star',
                text: '收藏',
                action: 'favorite',
                handler: () => this.favoriteFile(fileId)
            },
            {
                icon: 'fas fa-share',
                text: '分享',
                action: 'share',
                handler: () => this.shareFile(fileId)
            },
            { divider: true },
            {
                icon: 'fas fa-info-circle',
                text: '详细信息',
                action: 'info',
                handler: () => this.showFileInfo(fileId)
            }
        ];
        
        Components.ContextMenu.show(x, y, menuItems);
    }
    
    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        try {
            const response = await FileAPI.downloadFile(fileId);
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            
            // 获取文件名
            const contentDisposition = response.headers.get('content-disposition');
            let filename = 'download';
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) filename = matches[1];
            }
            
            Utils.url.downloadFile(url, filename);
            URL.revokeObjectURL(url);
            
            Components.Toast.success('文件下载成功');
        } catch (error) {
            CONFIG.log('error', 'Download failed:', error);
            Components.Toast.error('文件下载失败');
        }
    }
    
    /**
     * 预览文件
     */
    async previewFile(fileId) {
        try {
            const file = this.files.find(f => f.id === fileId);
            if (!file) return;
            
            const previewModal = Utils.dom.$('#preview-modal');
            const previewTitle = Utils.dom.$('#preview-title');
            const previewContainer = Utils.dom.$('#preview-container');
            
            if (!previewModal || !previewTitle || !previewContainer) return;
            
            previewTitle.textContent = file.name;
            previewContainer.innerHTML = '<div class="loading-spinner"><div class="spinner"></div></div>';
            
            Components.Modal.show('preview-modal');
            
            if (Utils.isImageFile(file.name)) {
                const img = Utils.dom.create('img', {
                    src: FileAPI.getThumbnailURL(fileId, 'large'),
                    alt: file.name
                });
                previewContainer.innerHTML = '';
                previewContainer.appendChild(img);
            } else if (Utils.isVideoFile(file.name)) {
                const video = Utils.dom.create('video', {
                    controls: true,
                    src: `${CONFIG.API.BASE_URL}/files/stream/${fileId}`
                });
                previewContainer.innerHTML = '';
                previewContainer.appendChild(video);
            } else {
                previewContainer.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-file"></i>
                        <p>此文件类型不支持预览</p>
                        <button class="btn btn-primary" onclick="fileManager.downloadFile('${fileId}')">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                `;
            }
        } catch (error) {
            CONFIG.log('error', 'Preview failed:', error);
            Components.Toast.error('文件预览失败');
        }
    }
    
    /**
     * 收藏文件
     */
    favoriteFile(fileId) {
        // TODO: 实现收藏功能
        Components.Toast.info('收藏功能开发中...');
    }
    
    /**
     * 分享文件
     */
    shareFile(fileId) {
        // TODO: 实现分享功能
        Components.Toast.info('分享功能开发中...');
    }
    
    /**
     * 显示文件信息
     */
    showFileInfo(fileId) {
        // TODO: 实现文件信息功能
        Components.Toast.info('文件信息功能开发中...');
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboard(event) {
        // Ctrl+A 全选
        if (event.ctrlKey && event.key === 'a') {
            event.preventDefault();
            this.selectAll();
        }
        
        // Delete 删除
        if (event.key === 'Delete') {
            this.deleteSelected();
        }
        
        // Escape 取消选择
        if (event.key === 'Escape') {
            this.clearSelection();
        }
    }
    
    /**
     * 全选文件
     */
    selectAll() {
        this.clearSelection();
        Utils.dom.$$('.file-item').forEach(item => {
            const fileId = item.dataset.fileId;
            this.selectFile(fileId, item);
        });
    }
    
    /**
     * 删除选中的文件
     */
    async deleteSelected() {
        if (this.selectedFiles.size === 0) return;
        
        const confirmed = await Components.Confirm.show(
            `确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`,
            '确认删除'
        );
        
        if (confirmed) {
            // TODO: 实现删除功能
            Components.Toast.info('删除功能开发中...');
        }
    }
    
    /**
     * 刷新文件列表
     */
    refresh() {
        this.loadFiles(this.currentFolder?.id);
    }
}

// 创建全局文件管理器实例
let fileManager;

document.addEventListener('DOMContentLoaded', () => {
    fileManager = new FileManager();
});

// 全局可用
window.FileManager = FileManager;
window.fileManager = null;
