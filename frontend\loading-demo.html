<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片文件系统 - 加载演示</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-notice {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .demo-notice h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .demo-notice p {
            margin: 0;
            opacity: 0.9;
        }
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
        }
        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .feature-card i {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .feature-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature-card p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-notice">
            <h2>🖼️ 图片文件系统演示</h2>
            <p>专门为图片文件优化的企业级文件分享系统</p>
        </div>

        <div class="main-content">
            <div class="status-section">
                <h3>🔧 系统状态</h3>
                <div id="status-indicators">
                    <div class="status-indicator status-loading">
                        <i class="fas fa-spinner loading-spinner"></i>
                        检查后端连接...
                    </div>
                    <div class="status-indicator status-loading">
                        <i class="fas fa-spinner loading-spinner"></i>
                        验证用户认证...
                    </div>
                    <div class="status-indicator status-loading">
                        <i class="fas fa-spinner loading-spinner"></i>
                        加载文件数据...
                    </div>
                </div>
            </div>

            <div class="demo-features">
                <div class="feature-card">
                    <i class="fas fa-images"></i>
                    <h3>图片专用</h3>
                    <p>支持JPG、PNG、PSD、AI、EPS等专业图片格式，自动过滤其他文件类型</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-eye"></i>
                    <h3>缩略图预览</h3>
                    <p>自动生成多尺寸缩略图，快速浏览大量图片文件</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-search"></i>
                    <h3>智能搜索</h3>
                    <p>支持文本搜索和图片搜索，快速找到目标文件</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-download"></i>
                    <h3>安全下载</h3>
                    <p>自动压缩打包，加密保护，支持密码申请机制</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-users"></i>
                    <h3>权限管理</h3>
                    <p>精细的用户权限控制，内外网访问权限分离</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>实时监控</h3>
                    <p>完整的操作日志，实时系统状态监控</p>
                </div>
            </div>

            <div class="action-buttons">
                <a href="login.html" class="btn">
                    <i class="fas fa-sign-in-alt"></i>
                    登录系统
                </a>
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i>
                    直接进入
                </a>
                <a href="debug-api.html" class="btn btn-secondary">
                    <i class="fas fa-bug"></i>
                    调试工具
                </a>
            </div>

            <div id="demo-message" style="text-align: center; margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 6px; display: none;">
                <p><strong>演示模式</strong>：由于后端服务器未连接，系统将显示演示数据。</p>
                <p>演示数据包含示例图片文件和文件夹，展示系统的完整功能。</p>
            </div>
        </div>
    </div>

    <!-- 包含必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>

    <script>
        let statusChecks = [
            { id: 'backend', name: '后端连接', checked: false },
            { id: 'auth', name: '用户认证', checked: false },
            { id: 'data', name: '文件数据', checked: false }
        ];

        async function checkSystemStatus() {
            const indicators = document.querySelectorAll('.status-indicator');
            
            // 检查后端连接
            try {
                const response = await fetch(`${CONFIG.API.BASE_URL}/api/health`, {
                    timeout: 5000
                });
                
                if (response.ok) {
                    updateStatus(0, 'success', '后端连接正常');
                    statusChecks[0].checked = true;
                } else {
                    updateStatus(0, 'error', '后端响应异常');
                }
            } catch (error) {
                updateStatus(0, 'error', '后端连接失败');
            }

            // 检查用户认证
            setTimeout(() => {
                const authData = localStorage.getItem('fileShareAuth');
                if (authData) {
                    try {
                        const auth = JSON.parse(authData);
                        if (auth.token) {
                            updateStatus(1, 'success', '用户已登录');
                            statusChecks[1].checked = true;
                        } else {
                            updateStatus(1, 'error', '认证信息无效');
                        }
                    } catch (e) {
                        updateStatus(1, 'error', '认证数据损坏');
                    }
                } else {
                    updateStatus(1, 'error', '用户未登录');
                }
            }, 1000);

            // 检查文件数据
            setTimeout(async () => {
                try {
                    if (statusChecks[0].checked && statusChecks[1].checked) {
                        // 尝试获取文件数据
                        const response = await fetch(`${CONFIG.API.BASE_URL}/api/files`, {
                            headers: {
                                'Authorization': `Bearer ${JSON.parse(localStorage.getItem('fileShareAuth')).token}`
                            }
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            updateStatus(2, 'success', `加载了 ${data.files?.length || 0} 个文件`);
                            statusChecks[2].checked = true;
                        } else {
                            updateStatus(2, 'error', '文件数据加载失败');
                        }
                    } else {
                        updateStatus(2, 'error', '依赖检查失败');
                    }
                } catch (error) {
                    updateStatus(2, 'error', '数据加载异常');
                }
                
                // 显示最终结果
                setTimeout(showFinalResult, 500);
            }, 2000);
        }

        function updateStatus(index, status, message) {
            const indicators = document.querySelectorAll('.status-indicator');
            const indicator = indicators[index];
            
            indicator.className = `status-indicator status-${status}`;
            
            let icon = '';
            switch (status) {
                case 'success':
                    icon = '<i class="fas fa-check"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-times"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-spinner loading-spinner"></i>';
            }
            
            indicator.innerHTML = `${icon} ${message}`;
        }

        function showFinalResult() {
            const allChecked = statusChecks.every(check => check.checked);
            
            if (!allChecked) {
                // 显示演示模式消息
                document.getElementById('demo-message').style.display = 'block';
                
                // 3秒后自动跳转到主页面（演示模式）
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 3000);
            } else {
                // 系统正常，跳转到主页面
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }
        }

        // 页面加载时开始检查
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 500);
        });
    </script>
</body>
</html>
