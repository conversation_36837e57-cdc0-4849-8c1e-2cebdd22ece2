<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件分享系统 - 登录</title>
    <link rel="stylesheet" href="css/login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="bg-shape shape-1"></div>
            <div class="bg-shape shape-2"></div>
            <div class="bg-shape shape-3"></div>
        </div>
        
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-cloud"></i>
                </div>
                <h1>文件分享系统</h1>
                <p>企业级安全文件共享平台</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        用户名
                    </label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        密码
                    </label>
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                    <button type="button" class="password-toggle" id="passwordToggle">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                
                <div class="form-group">
                    <label for="serverUrl">
                        <i class="fas fa-server"></i>
                        服务器地址
                    </label>
                    <input type="text" id="serverUrl" name="serverUrl" placeholder="http://localhost:8081" required>
                </div>
                
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        记住登录信息
                    </label>
                    
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoConnect">
                        <span class="checkmark"></span>
                        自动连接
                    </label>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <span class="btn-text">登录</span>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
                
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <div id="successMessage" class="success-message" style="display: none;"></div>
            </form>
            
            <div class="login-footer">
                <div class="connection-status" id="connectionStatus">
                    <i class="fas fa-circle status-icon"></i>
                    <span class="status-text">未连接</span>
                </div>
                
                <div class="system-info">
                    <small>版本 1.0.0 | 企业级文件共享系统</small>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/login.js"></script>

    <script>
        // 检查API服务器状态并更新连接状态显示
        async function checkAPIStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusIcon = statusElement.querySelector('.status-icon');
            const statusText = statusElement.querySelector('.status-text');

            try {
                const response = await fetch('http://localhost:8086/api/health', {
                    timeout: 3000
                });

                if (response.ok) {
                    statusIcon.className = 'fas fa-circle status-icon status-online';
                    statusText.textContent = 'API服务器在线';

                    // 自动填充服务器地址
                    const serverUrlInput = document.getElementById('serverUrl');
                    if (!serverUrlInput.value) {
                        serverUrlInput.value = 'http://localhost:8086';
                    }
                } else {
                    throw new Error('API响应异常');
                }
            } catch (error) {
                statusIcon.className = 'fas fa-circle status-icon status-offline';
                statusText.textContent = 'API服务器离线';

                // 显示启动指南链接
                setTimeout(() => {
                    if (confirm('API服务器未启动。是否查看启动指南？')) {
                        window.location.href = 'startup-guide.html';
                    }
                }, 2000);
            }
        }

        // 页面加载时检查API状态
        window.addEventListener('load', () => {
            checkAPIStatus();

            // 每10秒检查一次
            setInterval(checkAPIStatus, 10000);
        });
    </script>

    <style>
        .status-online {
            color: #28a745 !important;
        }
        .status-offline {
            color: #dc3545 !important;
        }
        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }
        .status-icon {
            font-size: 12px;
            color: #6c757d;
        }
        .status-text {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</body>
</html>
