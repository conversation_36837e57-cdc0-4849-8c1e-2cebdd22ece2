#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级文件共享系统 - 服务端主程序
作者: 系统开发团队
版本: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import SystemSettings
from config.database import DatabaseManager
from gui.main_window import MainWindow
from services.file_service import FileService
from services.search_service_simple import SearchService
from services.monitoring_service import MonitoringService
from api.server import APIServer
from utils.logger import setup_logger

class FileShareServer:
    """文件共享系统服务端主类"""
    
    def __init__(self):
        self.logger = setup_logger("FileShareServer")
        self.settings = SystemSettings()
        self.db_manager = DatabaseManager()
        self.api_server = None
        self.services = {}
        self.running = False
        
        # 初始化GUI
        self.root = tk.Tk()
        self.main_window = MainWindow(self.root, self)
        
        self.logger.info("文件共享系统服务端初始化完成")
    
    def initialize_services(self):
        """初始化所有服务"""
        try:
            # 初始化数据库
            self.db_manager.initialize()
            
            # 初始化各种服务
            self.services['file'] = FileService(self.db_manager)
            self.services['search'] = SearchService(self.db_manager)
            self.services['monitoring'] = MonitoringService(self.db_manager)
            
            # 初始化API服务器
            self.api_server = APIServer(self.services, self.settings)
            
            self.logger.info("所有服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            messagebox.showerror("错误", f"服务初始化失败: {e}")
            return False
    
    def start_server(self):
        """启动服务器"""
        if self.running:
            return
            
        try:
            if not self.initialize_services():
                return
                
            # 在新线程中启动API服务器
            server_thread = threading.Thread(
                target=self.api_server.run,
                daemon=True
            )
            server_thread.start()
            
            self.running = True
            self.main_window.update_status("服务器运行中", "green")
            self.logger.info("文件共享服务器启动成功")
            
        except Exception as e:
            self.logger.error(f"服务器启动失败: {e}")
            messagebox.showerror("错误", f"服务器启动失败: {e}")
    
    def stop_server(self):
        """停止服务器"""
        if not self.running:
            return
            
        try:
            if self.api_server:
                self.api_server.stop()
            
            self.running = False
            self.main_window.update_status("服务器已停止", "red")
            self.logger.info("文件共享服务器已停止")
            
        except Exception as e:
            self.logger.error(f"服务器停止失败: {e}")
    
    def get_server_status(self):
        """获取服务器状态"""
        return {
            'running': self.running,
            'services': len(self.services),
            'uptime': time.time() - self.start_time if hasattr(self, 'start_time') else 0
        }
    
    def run(self):
        """运行主程序"""
        try:
            self.start_time = time.time()
            
            # 设置窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 启动GUI主循环
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"程序运行错误: {e}")
            messagebox.showerror("错误", f"程序运行错误: {e}")
    
    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出文件共享系统吗？"):
            self.stop_server()
            self.root.destroy()

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 创建并运行服务器
        server = FileShareServer()
        server.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
