<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面 - 文件分享系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>文件分享系统 - 调试页面</h1>
        
        <div class="debug-section">
            <h3>1. 登录状态检查</h3>
            <div id="authStatus">检查中...</div>
            <button onclick="checkAuthStatus()">重新检查</button>
            <button onclick="clearAuth()">清除登录信息</button>
        </div>
        
        <div class="debug-section">
            <h3>2. API连接测试</h3>
            <div id="apiStatus">检查中...</div>
            <button onclick="testApiConnection()">测试连接</button>
        </div>
        
        <div class="debug-section">
            <h3>3. JavaScript模块加载</h3>
            <div id="moduleStatus">检查中...</div>
            <button onclick="checkModules()">检查模块</button>
        </div>
        
        <div class="debug-section">
            <h3>4. 本地存储信息</h3>
            <div id="storageInfo">检查中...</div>
            <button onclick="showStorageInfo()">显示存储信息</button>
        </div>
        
        <div class="debug-section">
            <h3>5. 控制台日志</h3>
            <div id="consoleLog">
                <pre id="logOutput">等待日志...</pre>
            </div>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="debug-section">
            <h3>6. 操作</h3>
            <button onclick="goToLogin()">跳转到登录页</button>
            <button onclick="goToIndex()">跳转到主页</button>
            <button onclick="simulateLogin()">模拟登录</button>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 日志收集
        const logs = [];
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        ['log', 'error', 'warn', 'info'].forEach(method => {
            console[method] = function(...args) {
                logs.push({
                    level: method,
                    message: args.join(' '),
                    timestamp: new Date().toISOString()
                });
                updateLogDisplay();
                originalConsole[method].apply(console, args);
            };
        });
        
        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            const recentLogs = logs.slice(-20); // 显示最近20条
            logOutput.textContent = recentLogs.map(log => 
                `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`
            ).join('\n');
        }
        
        function clearLog() {
            logs.length = 0;
            updateLogDisplay();
        }
        
        // 检查登录状态
        function checkAuthStatus() {
            const statusDiv = document.getElementById('authStatus');
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    statusDiv.innerHTML = '<span class="status-error">❌ 未找到登录信息</span>';
                    return;
                }
                
                const auth = JSON.parse(authData);
                const loginTime = new Date(auth.loginTime || 0);
                const now = new Date();
                const timeDiff = now - loginTime;
                const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                
                statusDiv.innerHTML = `
                    <div class="status-ok">✅ 找到登录信息</div>
                    <div>用户: ${auth.user?.username || '未知'}</div>
                    <div>服务器: ${auth.serverUrl || '未知'}</div>
                    <div>登录时间: ${loginTime.toLocaleString()}</div>
                    <div>已登录: ${hours} 小时</div>
                    <div>Token: ${auth.token ? '存在' : '缺失'}</div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `<span class="status-error">❌ 登录信息解析失败: ${error.message}</span>`;
            }
        }
        
        // 清除登录信息
        function clearAuth() {
            localStorage.removeItem('fileShareAuth');
            sessionStorage.removeItem('currentServerUrl');
            checkAuthStatus();
            console.log('已清除登录信息');
        }
        
        // 测试API连接
        async function testApiConnection() {
            const statusDiv = document.getElementById('apiStatus');
            statusDiv.innerHTML = '测试中...';
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                let serverUrl = 'http://localhost:8086';
                
                if (authData) {
                    const auth = JSON.parse(authData);
                    serverUrl = auth.serverUrl || serverUrl;
                }
                
                console.log('测试API连接:', serverUrl);
                
                const response = await fetch(`${serverUrl}/api/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.text();
                    statusDiv.innerHTML = `
                        <div class="status-ok">✅ API连接正常</div>
                        <div>服务器: ${serverUrl}</div>
                        <div>状态码: ${response.status}</div>
                        <div>响应: ${data}</div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="status-error">❌ API响应异常</div>
                        <div>状态码: ${response.status}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="status-error">❌ API连接失败: ${error.message}</span>`;
            }
        }
        
        // 检查模块
        function checkModules() {
            const statusDiv = document.getElementById('moduleStatus');
            const modules = [
                'CONFIG',
                'FileManager',
                'FileUploader', 
                'SearchManager',
                'NotificationManager',
                'Components',
                'Utils'
            ];
            
            const results = modules.map(module => {
                const exists = window[module] !== undefined;
                return `<div class="${exists ? 'status-ok' : 'status-error'}">${exists ? '✅' : '❌'} ${module}</div>`;
            });
            
            statusDiv.innerHTML = results.join('');
        }
        
        // 显示存储信息
        function showStorageInfo() {
            const statusDiv = document.getElementById('storageInfo');
            
            const storage = {
                localStorage: {},
                sessionStorage: {}
            };
            
            // 收集localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage.localStorage[key] = localStorage.getItem(key);
            }
            
            // 收集sessionStorage
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                storage.sessionStorage[key] = sessionStorage.getItem(key);
            }
            
            statusDiv.innerHTML = `<pre>${JSON.stringify(storage, null, 2)}</pre>`;
        }
        
        // 跳转函数
        function goToLogin() {
            window.location.href = 'login.html';
        }
        
        function goToIndex() {
            window.location.href = 'index.html';
        }
        
        // 模拟登录
        function simulateLogin() {
            const authInfo = {
                token: 'debug-token-' + Date.now(),
                user: {
                    username: 'debug-user',
                    role: 'user'
                },
                serverUrl: 'http://localhost:8086',
                loginTime: Date.now()
            };
            
            localStorage.setItem('fileShareAuth', JSON.stringify(authInfo));
            console.log('已模拟登录');
            checkAuthStatus();
        }
        
        // 页面加载时执行检查
        window.addEventListener('load', () => {
            checkAuthStatus();
            testApiConnection();
            checkModules();
            showStorageInfo();
        });
    </script>
</body>
</html>
