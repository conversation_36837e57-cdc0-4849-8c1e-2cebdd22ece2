#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
"""

import requests
import json
import time
import sys
import os

def test_api_server():
    """测试API服务器"""
    base_url = "http://localhost:8080"
    
    print("=" * 50)
    print("测试API服务器")
    print("=" * 50)
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("✓ 健康检查通过")
            print(f"  响应: {response.json()}")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 无法连接到API服务器: {e}")
        return False
    
    # 测试服务器状态
    try:
        response = requests.get(f"{base_url}/api/server/status", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器状态获取成功")
            print(f"  状态: {response.json()}")
        else:
            print(f"✗ 服务器状态获取失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 获取服务器状态失败: {e}")
    
    # 测试登录
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{base_url}/api/auth/login", 
                               json=login_data, timeout=5)
        if response.status_code == 200:
            print("✓ 登录测试通过")
            print(f"  响应: {response.json()}")
        else:
            print(f"✗ 登录测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 登录测试失败: {e}")
    
    # 测试文件夹列表
    try:
        response = requests.get(f"{base_url}/api/files/folders", timeout=5)
        if response.status_code == 200:
            print("✓ 文件夹列表获取成功")
            folders = response.json().get('folders', [])
            print(f"  共享文件夹数量: {len(folders)}")
        else:
            print(f"✗ 文件夹列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 文件夹列表获取失败: {e}")
    
    # 测试搜索
    try:
        search_data = {
            "query": "test",
            "type": "text"
        }
        response = requests.post(f"{base_url}/api/search", 
                               json=search_data, timeout=5)
        if response.status_code == 200:
            print("✓ 搜索功能测试通过")
            results = response.json()
            print(f"  搜索结果: {results}")
        else:
            print(f"✗ 搜索功能测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 搜索功能测试失败: {e}")
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("\n" + "=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    try:
        import pymysql
        
        # 测试连接
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ MySQL连接成功")
            print(f"  版本: {version[0]}")
            
            # 检查数据库是否存在
            cursor.execute("SHOW DATABASES LIKE 'file_share_system'")
            db_exists = cursor.fetchone()
            
            if db_exists:
                print("✓ 文件共享系统数据库存在")
                
                # 切换到数据库
                cursor.execute("USE file_share_system")
                
                # 检查表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"✓ 数据表数量: {len(tables)}")
                
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("! 文件共享系统数据库不存在（将在首次启动时创建）")
        
        connection.close()
        return True
        
    except ImportError:
        print("✗ PyMySQL未安装")
        return False
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_file_system():
    """测试文件系统"""
    print("\n" + "=" * 50)
    print("测试文件系统")
    print("=" * 50)
    
    # 检查必要目录
    directories = [
        "data",
        "data/search_index",
        "data/thumbnails",
        "logs",
        "temp",
        "backup"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✓ 目录存在: {directory}")
        else:
            print(f"✗ 目录不存在: {directory}")
    
    # 检查配置文件
    config_files = [
        "config/settings.py",
        "config/database.py"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 配置文件存在: {config_file}")
        else:
            print(f"✗ 配置文件不存在: {config_file}")
    
    return True

def test_services():
    """测试服务模块"""
    print("\n" + "=" * 50)
    print("测试服务模块")
    print("=" * 50)
    
    try:
        # 测试导入
        from config.settings import SystemSettings
        print("✓ 系统设置模块导入成功")
        
        from config.database import DatabaseManager
        print("✓ 数据库管理模块导入成功")
        
        from services.file_service import FileService
        print("✓ 文件服务模块导入成功")
        
        from services.search_service_simple import SearchService
        print("✓ 搜索服务模块导入成功")
        
        from services.monitoring_service import MonitoringService
        print("✓ 监控服务模块导入成功")
        
        # 测试设置
        settings = SystemSettings()
        print(f"✓ 系统设置加载成功")
        print(f"  服务器端口: {settings.get('server.port')}")
        print(f"  数据库主机: {settings.get('database.host')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 服务模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("企业级文件共享系统 - 系统测试")
    print("版本: 1.0.0")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 等待服务器启动
    print("\n等待服务器启动...")
    time.sleep(3)
    
    # 运行测试
    tests = [
        ("文件系统测试", test_file_system),
        ("服务模块测试", test_services),
        ("数据库连接测试", test_database_connection),
        ("API服务器测试", test_api_server)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
