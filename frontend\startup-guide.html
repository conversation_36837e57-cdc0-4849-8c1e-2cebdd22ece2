<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统启动指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .code {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .status-check {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-unknown { background: #6c757d; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 图片文件系统启动指南</h1>
            <p>企业级图片文件分享系统 - 完整启动流程</p>
        </div>

        <div class="warning">
            <strong>⚠️ 重要提示：</strong> 系统需要先启动后端服务器，然后才能使用前端界面。
        </div>

        <div class="step">
            <h3><span class="step-number">1</span>启动后端服务器</h3>
            <p>您已经启动了后端GUI程序，现在需要在GUI界面中启动API服务器：</p>
            <ul>
                <li>在后端GUI窗口中，点击 <strong>"启动服务器"</strong> 按钮</li>
                <li>或者使用菜单：<strong>服务器 → 启动服务器</strong></li>
                <li>等待状态栏显示 <strong>"服务器运行中"</strong></li>
            </ul>
        </div>

        <div class="step">
            <h3><span class="step-number">2</span>验证服务器状态</h3>
            <p>检查API服务器是否正常运行：</p>
            <div class="status-check">
                <div id="server-status">
                    <span class="status-indicator status-unknown"></span>
                    <span>检查中...</span>
                </div>
            </div>
            <button onclick="checkServerStatus()" class="btn">重新检查</button>
        </div>

        <div class="step">
            <h3><span class="step-number">3</span>登录系统</h3>
            <p>服务器启动后，您可以：</p>
            <ul>
                <li>使用默认管理员账户登录</li>
                <li>用户名：<code>admin</code></li>
                <li>密码：<code>admin123</code></li>
            </ul>
            <a href="login.html" class="btn">前往登录页面</a>
        </div>

        <div class="step">
            <h3><span class="step-number">4</span>开始使用</h3>
            <p>登录成功后，您可以：</p>
            <ul>
                <li>📁 管理共享文件夹</li>
                <li>🖼️ 浏览图片文件</li>
                <li>🔍 搜索文件</li>
                <li>📥 下载文件</li>
                <li>⚙️ 配置系统设置</li>
            </ul>
        </div>

        <div class="success" id="success-message" style="display: none;">
            <strong>✅ 系统已就绪！</strong> API服务器正在运行，您可以开始使用系统了。
        </div>

        <div class="step">
            <h3>🔧 故障排除</h3>
            <p>如果遇到问题，请检查：</p>
            <ul>
                <li>确保后端GUI程序正在运行</li>
                <li>在GUI中点击了"启动服务器"按钮</li>
                <li>检查防火墙是否阻止了端口 8086</li>
                <li>查看后端控制台的错误信息</li>
            </ul>
            <a href="test-connection.html" class="btn btn-secondary">连接测试工具</a>
        </div>
    </div>

    <script>
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            const successMessage = document.getElementById('success-message');
            
            statusElement.innerHTML = '<span class="status-indicator status-unknown"></span><span>检查中...</span>';
            
            try {
                const response = await fetch('http://localhost:8086/api/health', {
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = '<span class="status-indicator status-success"></span><span>✅ API服务器运行正常</span>';
                    successMessage.style.display = 'block';
                } else {
                    statusElement.innerHTML = '<span class="status-indicator status-error"></span><span>❌ API服务器响应异常</span>';
                    successMessage.style.display = 'none';
                }
            } catch (error) {
                statusElement.innerHTML = '<span class="status-indicator status-error"></span><span>❌ 无法连接到API服务器</span>';
                successMessage.style.display = 'none';
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkServerStatus, 1000);
            
            // 每10秒自动检查一次
            setInterval(checkServerStatus, 10000);
        });
    </script>
</body>
</html>
