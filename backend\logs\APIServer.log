2025-06-08 00:01:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:08:16 - APIServer - INFO - API服务器已停止
2025-06-08 00:08:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:17:45 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:17:45 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 00:17:51 - APIServer - INFO - API服务器已停止
2025-06-08 00:17:53 - APIServer - INFO - API服务器已停止
2025-06-08 00:17:54 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:19:13 - APIServer - INFO - API服务器已停止
2025-06-08 00:22:25 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:32:46 - APIServer - INFO - API服务器已停止
2025-06-08 00:32:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:36:26 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:29 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:30 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:37:27 - APIServer - INFO - API服务器已停止
2025-06-08 00:37:33 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:38:49 - APIServer - INFO - API服务器已停止
2025-06-08 00:46:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:20 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:53:17 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:55:35 - APIServer - INFO - API服务器已停止
2025-06-08 00:55:44 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
