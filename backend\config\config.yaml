database:
  charset: utf8mb4
  database: file_share_system
  host: localhost
  password: '123456'
  port: 3306
  username: root
download:
  enable_batch_download: true
  enable_folder_download: true
  enable_single_download: true
  encryption_after_downloads: 3
  max_batch_files: 100
  max_package_size: 524288000
  password_request_limit: 5
file_share:
  allowed_extensions:
  - .jpg
  - .jpeg
  - .png
  - .gif
  - .bmp
  - .tiff
  - .tif
  - .psd
  - .ai
  - .eps
  - .pdf
  - .doc
  - .docx
  - .xls
  - .xlsx
  - .ppt
  - .pptx
  - .txt
  - .zip
  - .rar
  - .7z
  max_file_size: 1073741824
  shared_folders: []
  thumbnail_sizes:
    large: !!python/tuple
    - 600
    - 600
    medium: !!python/tuple
    - 300
    - 300
    small: !!python/tuple
    - 150
    - 150
    xlarge: !!python/tuple
    - 1200
    - 1200
monitoring:
  alert_thresholds:
    max_concurrent_users: 100
    max_download_speed: 10485760
    max_search_per_minute: 60
  enable_activity_log: true
  enable_real_time_monitor: true
  log_retention_days: 90
network:
  enable_external_access: false
  enable_internal_access: true
  internal_networks:
  - ***********/16
  - 10.0.0.0/8
  - **********/12
  rate_limit:
    downloads_per_hour: 100
    requests_per_minute: 60
notifications:
  enable_rolling_notifications: true
  enable_screenshots: true
  max_notifications: 10
  notification_duration: 5000
permissions:
  admin_permissions:
  - read
  - write
  - delete
  - admin
  default_user_permissions:
  - read
  guest_permissions:
  - read
search:
  enable_image_search: true
  enable_text_search: true
  index_path: ./data/search_index
  max_search_results: 1000
security:
  ban_duration: 300
  enable_registration: false
  max_login_attempts: 5
  require_license_key: true
  sensitive_file_patterns:
  - '*secret*'
  - '*private*'
  - '*confidential*'
  session_timeout: 3600
server:
  debug: false
  host: 0.0.0.0
  max_workers: 10
  port: 8081
  timeout: 30
system:
  backup_directory: ./backup
  data_directory: ./data
  language: zh_CN
  log_directory: ./logs
  temp_directory: ./temp
  timezone: Asia/Shanghai
