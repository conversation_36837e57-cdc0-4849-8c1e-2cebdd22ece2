#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理服务
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import mimetypes

from models.file_share import SharedFolder, SharedFile
from utils.logger import setup_logger

class FileService:
    """文件管理服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("FileService")
    
    def scan_shared_folder(self, folder_id: int) -> Dict[str, Any]:
        """扫描共享文件夹，更新文件信息"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                if not folder.is_path_valid():
                    return {"success": False, "error": "文件夹路径无效"}
                
                # 扫描文件
                scanned_files = []
                total_size = 0
                
                for root, dirs, files in os.walk(folder.path):
                    for filename in files:
                        file_path = os.path.join(root, filename)
                        relative_path = os.path.relpath(file_path, folder.path)
                        
                        try:
                            # 获取文件信息
                            stat = os.stat(file_path)
                            file_size = stat.st_size
                            file_modified = datetime.fromtimestamp(stat.st_mtime)
                            
                            # 检查文件是否已存在
                            existing_file = session.query(SharedFile).filter_by(
                                folder_id=folder_id,
                                relative_path=relative_path
                            ).first()
                            
                            if existing_file:
                                # 更新现有文件信息
                                existing_file.file_size = file_size
                                existing_file.file_modified = file_modified
                                existing_file.update_file_info()
                            else:
                                # 创建新文件记录
                                new_file = SharedFile(
                                    folder_id=folder_id,
                                    filename=filename,
                                    relative_path=relative_path,
                                    file_size=file_size,
                                    file_modified=file_modified
                                )
                                new_file.update_file_info()
                                session.add(new_file)
                            
                            scanned_files.append(relative_path)
                            total_size += file_size
                            
                        except Exception as e:
                            self.logger.warning(f"处理文件失败 {file_path}: {e}")
                            continue
                
                # 删除不存在的文件记录
                existing_files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                for file_record in existing_files:
                    if file_record.relative_path not in scanned_files:
                        session.delete(file_record)
                
                # 更新文件夹统计信息
                folder.file_count = len(scanned_files)
                folder.total_size = total_size
                folder.last_scanned = datetime.now()
                
                session.commit()
                
                self.logger.info(f"文件夹扫描完成: {folder.name}, 文件数: {len(scanned_files)}")
                
                return {
                    "success": True,
                    "files_count": len(scanned_files),
                    "total_size": total_size,
                    "folder_name": folder.name
                }
                
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_file_info(self, file_id: int) -> Optional[Dict[str, Any]]:
        """获取文件详细信息"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return None
                
                file_info = file_record.to_dict()
                
                # 添加实时文件信息
                full_path = file_record.get_full_path()
                if os.path.exists(full_path):
                    stat = os.stat(full_path)
                    file_info['current_size'] = stat.st_size
                    file_info['current_modified'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
                    file_info['exists'] = True
                else:
                    file_info['exists'] = False
                
                return file_info
                
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return None
    
    def get_folder_files(self, folder_id: int, page: int = 1, 
                        page_size: int = 50, search_query: str = None) -> Dict[str, Any]:
        """获取文件夹中的文件列表"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(SharedFile).filter_by(folder_id=folder_id)
                
                # 搜索过滤
                if search_query:
                    query = query.filter(SharedFile.filename.contains(search_query))
                
                # 分页
                total_count = query.count()
                offset = (page - 1) * page_size
                files = query.offset(offset).limit(page_size).all()
                
                files_data = [file.to_dict() for file in files]
                
                return {
                    "success": True,
                    "files": files_data,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
                
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def create_shared_folder(self, name: str, path: str, **kwargs) -> Dict[str, Any]:
        """创建共享文件夹"""
        try:
            # 验证路径
            if not os.path.exists(path) or not os.path.isdir(path):
                return {"success": False, "error": "路径不存在或不是文件夹"}
            
            with self.db_manager.get_session() as session:
                # 检查是否已存在相同路径
                existing = session.query(SharedFolder).filter_by(path=path).first()
                if existing:
                    return {"success": False, "error": "该路径已被共享"}
                
                # 创建共享文件夹
                folder = SharedFolder(name=name, path=path, **kwargs)
                session.add(folder)
                session.commit()
                
                # 扫描文件夹
                scan_result = self.scan_shared_folder(folder.id)
                
                self.logger.info(f"创建共享文件夹成功: {name} -> {path}")
                
                return {
                    "success": True,
                    "folder_id": folder.id,
                    "scan_result": scan_result
                }
                
        except Exception as e:
            self.logger.error(f"创建共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def update_shared_folder(self, folder_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新共享文件夹设置"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 更新属性
                for key, value in updates.items():
                    if hasattr(folder, key):
                        setattr(folder, key, value)
                
                session.commit()
                
                self.logger.info(f"更新共享文件夹成功: {folder.name}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"更新共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_shared_folder(self, folder_id: int) -> Dict[str, Any]:
        """删除共享文件夹"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                folder_name = folder.name
                session.delete(folder)
                session.commit()
                
                self.logger.info(f"删除共享文件夹成功: {folder_name}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"删除共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_shared_folders(self) -> List[Dict[str, Any]]:
        """获取所有共享文件夹"""
        try:
            with self.db_manager.get_session() as session:
                folders = session.query(SharedFolder).all()
                return [folder.to_dict() for folder in folders]
                
        except Exception as e:
            self.logger.error(f"获取共享文件夹列表失败: {e}")
            return []
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def get_file_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or "application/octet-stream"
    
    def is_file_allowed(self, filename: str, folder_id: int) -> bool:
        """检查文件是否被允许"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder or not folder.allowed_extensions:
                    return True
                
                _, ext = os.path.splitext(filename)
                return ext.lower() in folder.allowed_extensions
                
        except Exception as e:
            self.logger.error(f"检查文件权限失败: {e}")
            return False
    
    def get_folder_statistics(self, folder_id: int) -> Dict[str, Any]:
        """获取文件夹统计信息"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 文件类型统计
                files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                
                stats = {
                    "total_files": len(files),
                    "total_size": folder.total_size,
                    "file_types": {},
                    "size_distribution": {
                        "small": 0,    # < 1MB
                        "medium": 0,   # 1MB - 10MB
                        "large": 0,    # 10MB - 100MB
                        "xlarge": 0    # > 100MB
                    }
                }
                
                for file in files:
                    # 文件类型统计
                    ext = file.extension or "unknown"
                    stats["file_types"][ext] = stats["file_types"].get(ext, 0) + 1
                    
                    # 大小分布统计
                    size_mb = file.file_size / (1024 * 1024)
                    if size_mb < 1:
                        stats["size_distribution"]["small"] += 1
                    elif size_mb < 10:
                        stats["size_distribution"]["medium"] += 1
                    elif size_mb < 100:
                        stats["size_distribution"]["large"] += 1
                    else:
                        stats["size_distribution"]["xlarge"] += 1
                
                return {"success": True, "statistics": stats}
                
        except Exception as e:
            self.logger.error(f"获取文件夹统计失败: {e}")
            return {"success": False, "error": str(e)}
