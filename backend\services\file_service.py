#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理服务
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import mimetypes

from models.file_share import SharedFolder, SharedFile
from utils.logger import setup_logger

class FileService:
    """文件管理服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("FileService")
    
    def scan_shared_folder(self, folder_id: int) -> Dict[str, Any]:
        """扫描共享文件夹，更新文件信息"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                if not folder.is_path_valid():
                    return {"success": False, "error": "文件夹路径无效"}
                
                # 扫描文件
                scanned_files = []
                total_size = 0
                
                for root, dirs, files in os.walk(folder.path):
                    for filename in files:
                        file_path = os.path.join(root, filename)
                        relative_path = os.path.relpath(file_path, folder.path)
                        
                        try:
                            # 获取文件信息
                            stat = os.stat(file_path)
                            file_size = stat.st_size
                            file_modified = datetime.fromtimestamp(stat.st_mtime)
                            
                            # 检查文件是否已存在
                            existing_file = session.query(SharedFile).filter_by(
                                folder_id=folder_id,
                                relative_path=relative_path
                            ).first()
                            
                            if existing_file:
                                # 更新现有文件信息
                                existing_file.file_size = file_size
                                existing_file.file_modified = file_modified
                                existing_file.update_file_info()
                            else:
                                # 创建新文件记录
                                new_file = SharedFile(
                                    folder_id=folder_id,
                                    filename=filename,
                                    relative_path=relative_path,
                                    file_size=file_size,
                                    file_modified=file_modified
                                )
                                new_file.update_file_info()
                                session.add(new_file)
                            
                            scanned_files.append(relative_path)
                            total_size += file_size
                            
                        except Exception as e:
                            self.logger.warning(f"处理文件失败 {file_path}: {e}")
                            continue
                
                # 删除不存在的文件记录
                existing_files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                for file_record in existing_files:
                    if file_record.relative_path not in scanned_files:
                        session.delete(file_record)
                
                # 更新文件夹统计信息
                folder.file_count = len(scanned_files)
                folder.total_size = total_size
                folder.last_scanned = datetime.now()
                
                session.commit()
                
                self.logger.info(f"文件夹扫描完成: {folder.name}, 文件数: {len(scanned_files)}")
                
                return {
                    "success": True,
                    "files_count": len(scanned_files),
                    "total_size": total_size,
                    "folder_name": folder.name
                }
                
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_file_info(self, file_id: int) -> Optional[Dict[str, Any]]:
        """获取文件详细信息"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return None
                
                file_info = file_record.to_dict()
                
                # 添加实时文件信息
                full_path = file_record.get_full_path()
                if os.path.exists(full_path):
                    stat = os.stat(full_path)
                    file_info['current_size'] = stat.st_size
                    file_info['current_modified'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
                    file_info['exists'] = True
                else:
                    file_info['exists'] = False
                
                return file_info
                
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return None
    
    def get_folder_files(self, folder_id: int, page: int = 1, 
                        page_size: int = 50, search_query: str = None) -> Dict[str, Any]:
        """获取文件夹中的文件列表"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(SharedFile).filter_by(folder_id=folder_id)
                
                # 搜索过滤
                if search_query:
                    query = query.filter(SharedFile.filename.contains(search_query))
                
                # 分页
                total_count = query.count()
                offset = (page - 1) * page_size
                files = query.offset(offset).limit(page_size).all()
                
                files_data = [file.to_dict() for file in files]
                
                return {
                    "success": True,
                    "files": files_data,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
                
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def create_shared_folder(self, name: str, path: str, **kwargs) -> Dict[str, Any]:
        """创建共享文件夹"""
        try:
            # 验证路径
            if not os.path.exists(path) or not os.path.isdir(path):
                return {"success": False, "error": "路径不存在或不是文件夹"}
            
            with self.db_manager.get_session() as session:
                # 检查是否已存在相同路径
                existing = session.query(SharedFolder).filter_by(path=path).first()
                if existing:
                    return {"success": False, "error": "该路径已被共享"}
                
                # 创建共享文件夹
                folder = SharedFolder(name=name, path=path, **kwargs)
                session.add(folder)
                session.commit()
                
                # 扫描文件夹
                scan_result = self.scan_shared_folder(folder.id)
                
                self.logger.info(f"创建共享文件夹成功: {name} -> {path}")
                
                return {
                    "success": True,
                    "folder_id": folder.id,
                    "scan_result": scan_result
                }
                
        except Exception as e:
            self.logger.error(f"创建共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def update_shared_folder(self, folder_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新共享文件夹设置"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 更新属性
                for key, value in updates.items():
                    if hasattr(folder, key):
                        setattr(folder, key, value)
                
                session.commit()
                
                self.logger.info(f"更新共享文件夹成功: {folder.name}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"更新共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_shared_folder(self, folder_id: int) -> Dict[str, Any]:
        """删除共享文件夹"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                folder_name = folder.name
                session.delete(folder)
                session.commit()
                
                self.logger.info(f"删除共享文件夹成功: {folder_name}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"删除共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_shared_folders(self) -> Dict[str, Any]:
        """获取所有共享文件夹"""
        try:
            with self.db_manager.get_session() as session:
                folders = session.query(SharedFolder).all()

                folder_list = []
                for folder in folders:
                    # 统计文件数量
                    file_count = session.query(SharedFile).filter(
                        SharedFile.folder_id == folder.id
                    ).count()

                    folder_dict = folder.to_dict()
                    folder_dict['file_count'] = file_count
                    folder_list.append(folder_dict)

                return {
                    'success': True,
                    'folders': folder_list
                }

        except Exception as e:
            self.logger.error(f"获取共享文件夹列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def get_file_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or "application/octet-stream"
    
    def is_file_allowed(self, filename: str, folder_id: int) -> bool:
        """检查文件是否被允许"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder or not folder.allowed_extensions:
                    return True
                
                _, ext = os.path.splitext(filename)
                return ext.lower() in folder.allowed_extensions
                
        except Exception as e:
            self.logger.error(f"检查文件权限失败: {e}")
            return False
    
    def get_folder_statistics(self, folder_id: int) -> Dict[str, Any]:
        """获取文件夹统计信息"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 文件类型统计
                files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                
                stats = {
                    "total_files": len(files),
                    "total_size": folder.total_size,
                    "file_types": {},
                    "size_distribution": {
                        "small": 0,    # < 1MB
                        "medium": 0,   # 1MB - 10MB
                        "large": 0,    # 10MB - 100MB
                        "xlarge": 0    # > 100MB
                    }
                }
                
                for file in files:
                    # 文件类型统计
                    ext = file.extension or "unknown"
                    stats["file_types"][ext] = stats["file_types"].get(ext, 0) + 1
                    
                    # 大小分布统计
                    size_mb = file.file_size / (1024 * 1024)
                    if size_mb < 1:
                        stats["size_distribution"]["small"] += 1
                    elif size_mb < 10:
                        stats["size_distribution"]["medium"] += 1
                    elif size_mb < 100:
                        stats["size_distribution"]["large"] += 1
                    else:
                        stats["size_distribution"]["xlarge"] += 1
                
                return {"success": True, "statistics": stats}

        except Exception as e:
            self.logger.error(f"获取文件夹统计失败: {e}")
            return {"success": False, "error": str(e)}

    def get_folder_files(self, folder_id: int, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """获取指定文件夹的文件列表"""
        try:
            with self.db_manager.get_session() as session:
                # 检查文件夹是否存在
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()

                if not folder:
                    return {
                        'success': False,
                        'error': '共享文件夹不存在'
                    }

                # 查询文件记录
                query = session.query(SharedFile).filter(
                    SharedFile.folder_id == folder_id
                )

                total_count = query.count()

                # 分页
                offset = (page - 1) * page_size
                files = query.offset(offset).limit(page_size).all()

                file_list = []
                for file_record in files:
                    file_dict = file_record.to_dict()
                    file_list.append(file_dict)

                return {
                    'success': True,
                    'files': file_list,
                    'total_count': total_count,
                    'page': page,
                    'page_size': page_size
                }

        except Exception as e:
            self.logger.error(f"获取文件列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_file_record(self, file_id: int) -> Dict[str, Any]:
        """删除文件记录（不删除实际文件）"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()

                if not file_record:
                    return {
                        'success': False,
                        'error': '文件记录不存在'
                    }

                file_name = file_record.filename
                session.delete(file_record)
                session.commit()

                self.logger.info(f"删除文件记录成功: {file_name}")

                return {
                    'success': True,
                    'message': '文件记录删除成功'
                }

        except Exception as e:
            self.logger.error(f"删除文件记录失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def scan_folder(self, folder_id: int) -> Dict[str, Any]:
        """扫描指定文件夹"""
        try:
            return self.scan_shared_folder(folder_id)
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def set_folder_permissions(self, folder_id: int, permissions: Dict[str, Any]) -> Dict[str, Any]:
        """设置文件夹权限"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 更新网络访问权限
                network_access = permissions.get('network_access', {})
                folder.allow_internal = network_access.get('internal', True)
                folder.allow_external = network_access.get('external', False)

                # 更新文件操作权限
                file_operations = permissions.get('file_operations', {})
                folder.allow_read = file_operations.get('read', True)
                folder.allow_write = file_operations.get('write', False)
                folder.allow_delete = file_operations.get('delete', False)
                folder.allow_upload = file_operations.get('write', False)  # 写入权限包含上传
                folder.allow_download = file_operations.get('read', True)  # 读取权限包含下载
                folder.show_details = file_operations.get('show_details', True)

                # 更新下载限制
                download_limits = permissions.get('download_limits', {})
                max_size_mb = download_limits.get('max_file_size_mb', 100)
                folder.max_file_size = max_size_mb * 1024 * 1024  # 转换为字节

                session.commit()

                self.logger.info(f"设置文件夹权限成功: {folder.name}")

                return {"success": True, "message": "权限设置已保存"}

        except Exception as e:
            self.logger.error(f"设置文件夹权限失败: {e}")
            return {"success": False, "error": str(e)}

    def get_folder_permissions(self, folder_id: int) -> Dict[str, Any]:
        """获取文件夹权限"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 从现有字段构建权限设置
                permissions = {
                    'network_access': {
                        'internal': folder.allow_internal,
                        'external': folder.allow_external
                    },
                    'file_operations': {
                        'read': folder.allow_read,
                        'write': folder.allow_write,
                        'delete': folder.allow_delete,
                        'replace': folder.allow_write,  # 替换权限等同于写入权限
                        'show_details': folder.show_details
                    },
                    'download_limits': {
                        'max_file_size_mb': int(folder.max_file_size / (1024 * 1024)) if folder.max_file_size > 0 else 100,
                        'max_batch_files': 10  # 默认值
                    },
                    'encryption': {
                        'encrypt_after_downloads': 5  # 默认值
                    }
                }

                return {"success": True, "permissions": permissions}

        except Exception as e:
            self.logger.error(f"获取文件夹权限失败: {e}")
            return {"success": False, "error": str(e)}

    def get_root_files(self, page: int = 1, page_size: int = 50, search_query: str = None) -> Dict[str, Any]:
        """获取根目录文件列表（所有共享文件夹的文件）"""
        try:
            with self.db_manager.get_session() as session:
                # 获取所有共享文件夹
                folders = session.query(SharedFolder).all()

                all_files = []

                # 遍历每个文件夹，获取文件
                for folder in folders:
                    query = session.query(SharedFile).filter_by(folder_id=folder.id)

                    # 搜索过滤
                    if search_query:
                        query = query.filter(SharedFile.filename.contains(search_query))

                    files = query.all()

                    # 添加文件夹信息到文件数据中
                    for file in files:
                        file_dict = file.to_dict()
                        file_dict['folder_name'] = folder.name
                        file_dict['folder_path'] = folder.path
                        all_files.append(file_dict)

                # 添加文件夹作为可导航的项目
                for folder in folders:
                    folder_dict = {
                        'id': folder.id,
                        'name': folder.name,
                        'type': 'folder',
                        'size': 0,
                        'modified_at': folder.created_at.isoformat() if folder.created_at else None,
                        'file_count': session.query(SharedFile).filter_by(folder_id=folder.id).count()
                    }
                    all_files.append(folder_dict)

                # 分页处理
                total_count = len(all_files)
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_files = all_files[start_idx:end_idx]

                return {
                    "success": True,
                    "files": paginated_files,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }

        except Exception as e:
            self.logger.error(f"获取根目录文件列表失败: {e}")
            return {"success": False, "error": str(e)}
