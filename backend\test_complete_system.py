#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统功能测试脚本
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

def test_user_management():
    """测试用户管理功能"""
    print("\n" + "=" * 50)
    print("测试用户管理功能")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    # 测试管理员登录
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('token')
                print("✓ 管理员登录成功")
                
                # 测试获取用户列表
                headers = {'Authorization': f'Bearer {token}'}
                response = requests.get(f"{base_url}/api/admin/users", headers=headers, timeout=5)
                
                if response.status_code == 200:
                    users_data = response.json()
                    print(f"✓ 获取用户列表成功，用户数: {users_data.get('total_count', 0)}")
                else:
                    print(f"✗ 获取用户列表失败: {response.status_code}")
                
                # 测试创建用户
                new_user_data = {
                    "username": "testuser",
                    "password": "testpass123",
                    "full_name": "测试用户",
                    "email": "<EMAIL>",
                    "user_group": "user"
                }
                
                response = requests.post(f"{base_url}/api/admin/users", 
                                       json=new_user_data, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("✓ 创建用户成功")
                        user_id = result.get('user_id')
                        
                        # 测试更新用户
                        update_data = {"full_name": "更新的测试用户"}
                        response = requests.put(f"{base_url}/api/admin/users/{user_id}", 
                                              json=update_data, headers=headers, timeout=5)
                        
                        if response.status_code == 200:
                            print("✓ 更新用户成功")
                        else:
                            print(f"✗ 更新用户失败: {response.status_code}")
                        
                        # 测试删除用户
                        response = requests.delete(f"{base_url}/api/admin/users/{user_id}", 
                                                 headers=headers, timeout=5)
                        
                        if response.status_code == 200:
                            print("✓ 删除用户成功")
                        else:
                            print(f"✗ 删除用户失败: {response.status_code}")
                    else:
                        print(f"✗ 创建用户失败: {result.get('error')}")
                else:
                    print(f"✗ 创建用户失败: {response.status_code}")
                
                return True
            else:
                print(f"✗ 管理员登录失败: {result.get('error')}")
        else:
            print(f"✗ 管理员登录失败: {response.status_code}")
    
    except Exception as e:
        print(f"✗ 用户管理测试失败: {e}")
    
    return False

def test_file_operations():
    """测试文件操作功能"""
    print("\n" + "=" * 50)
    print("测试文件操作功能")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    try:
        # 先登录获取token
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # 测试获取共享文件夹
            response = requests.get(f"{base_url}/api/files/folders", headers=headers, timeout=5)
            
            if response.status_code == 200:
                folders_data = response.json()
                folders = folders_data.get('folders', [])
                print(f"✓ 获取共享文件夹成功，数量: {len(folders)}")
                
                if folders:
                    folder_id = folders[0]['id']
                    
                    # 测试获取文件列表
                    response = requests.get(f"{base_url}/api/files/folders/{folder_id}/files", 
                                          headers=headers, timeout=5)
                    
                    if response.status_code == 200:
                        files_data = response.json()
                        if files_data.get('success'):
                            files = files_data.get('files', [])
                            print(f"✓ 获取文件列表成功，文件数: {len(files)}")
                            
                            if files:
                                file_id = files[0]['id']
                                
                                # 测试文件下载
                                response = requests.get(f"{base_url}/api/files/{file_id}/download", 
                                                      headers=headers, timeout=5)
                                
                                if response.status_code == 200:
                                    print("✓ 文件下载测试成功")
                                else:
                                    print(f"✗ 文件下载测试失败: {response.status_code}")
                                
                                # 测试批量下载
                                batch_data = {"file_ids": [file_id]}
                                response = requests.post(f"{base_url}/api/files/batch/download", 
                                                       json=batch_data, headers=headers, timeout=5)
                                
                                if response.status_code == 200:
                                    print("✓ 批量下载测试成功")
                                else:
                                    print(f"✗ 批量下载测试失败: {response.status_code}")
                        else:
                            print(f"✗ 获取文件列表失败: {files_data.get('error')}")
                    else:
                        print(f"✗ 获取文件列表失败: {response.status_code}")
                else:
                    print("! 没有共享文件夹，跳过文件操作测试")
            else:
                print(f"✗ 获取共享文件夹失败: {response.status_code}")
        else:
            print("✗ 登录失败，跳过文件操作测试")
    
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")

def test_search_functionality():
    """测试搜索功能"""
    print("\n" + "=" * 50)
    print("测试搜索功能")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    try:
        # 先登录获取token
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # 测试文本搜索
            search_data = {
                "query": "test",
                "type": "text"
            }
            response = requests.post(f"{base_url}/api/search", 
                                   json=search_data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                search_result = response.json()
                results = search_result.get('results', [])
                print(f"✓ 文本搜索成功，结果数: {len(results)}")
            else:
                print(f"✗ 文本搜索失败: {response.status_code}")
            
            # 测试图像搜索
            search_data = {
                "query": "image.jpg",
                "type": "image"
            }
            response = requests.post(f"{base_url}/api/search", 
                                   json=search_data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                search_result = response.json()
                results = search_result.get('results', [])
                print(f"✓ 图像搜索成功，结果数: {len(results)}")
            else:
                print(f"✗ 图像搜索失败: {response.status_code}")
        else:
            print("✗ 登录失败，跳过搜索测试")
    
    except Exception as e:
        print(f"✗ 搜索功能测试失败: {e}")

def test_system_statistics():
    """测试系统统计功能"""
    print("\n" + "=" * 50)
    print("测试系统统计功能")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    try:
        # 先登录获取token
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # 测试获取系统统计
            response = requests.get(f"{base_url}/api/admin/stats", headers=headers, timeout=5)
            
            if response.status_code == 200:
                stats = response.json()
                print("✓ 系统统计获取成功")
                print(f"  总用户数: {stats.get('total_users', 0)}")
                print(f"  在线用户: {stats.get('online_users', 0)}")
                print(f"  总文件数: {stats.get('total_files', 0)}")
                print(f"  今日下载: {stats.get('today_downloads', 0)}")
            else:
                print(f"✗ 系统统计获取失败: {response.status_code}")
        else:
            print("✗ 登录失败，跳过统计测试")
    
    except Exception as e:
        print(f"✗ 系统统计测试失败: {e}")

def test_api_performance():
    """测试API性能"""
    print("\n" + "=" * 50)
    print("测试API性能")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    try:
        # 测试健康检查响应时间
        start_time = time.time()
        response = requests.get(f"{base_url}/api/health", timeout=5)
        end_time = time.time()
        
        if response.status_code == 200:
            response_time = (end_time - start_time) * 1000
            print(f"✓ 健康检查响应时间: {response_time:.2f}ms")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
        
        # 测试并发请求
        import threading
        
        def make_request():
            try:
                requests.get(f"{base_url}/api/health", timeout=5)
                return True
            except:
                return False
        
        threads = []
        results = []
        
        start_time = time.time()
        for i in range(10):  # 10个并发请求
            thread = threading.Thread(target=lambda: results.append(make_request()))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        success_count = sum(results)
        total_time = (end_time - start_time) * 1000
        
        print(f"✓ 并发测试: {success_count}/10 成功，总时间: {total_time:.2f}ms")
    
    except Exception as e:
        print(f"✗ API性能测试失败: {e}")

def main():
    """主测试函数"""
    print("企业级文件共享系统 - 完整功能测试")
    print("版本: 1.0.0")
    print("时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 等待服务器启动
    print("\n等待服务器启动...")
    time.sleep(5)
    
    # 运行测试
    tests = [
        ("API性能测试", test_api_performance),
        ("用户管理测试", test_user_management),
        ("文件操作测试", test_file_operations),
        ("搜索功能测试", test_search_functionality),
        ("系统统计测试", test_system_statistics)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始执行: {test_name}")
            test_func()
            results.append((test_name, True))
        except Exception as e:
            print(f"✗ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
